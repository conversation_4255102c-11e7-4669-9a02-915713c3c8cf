**Auto Provisioning of any app via UMP**

**Base URL: UMPURL/onboarding**  
For e.g https://umpdev.unvired.io/onboarding

**API: /validatedomain**   
Verb: GET  
Input parameters: domain  
Return: 200 - Domain available  
        400 - Not available  
        500 - System error  

**API: /registerdomain**  
Verb: POST  
Input parameters: domain, app, firstname, lastname, email, password, phone (optional)  
Password: Make sure it follows policy - 8 chars length, uppercase, lowercase, number, special chars (eg.) Unvired123*)  
Return: 200 - Domain registered, awaiting confirmation via OTP  
        400 - Not available  
        500 - System error  

**API: /createdomain**  
Verb: POST  
Input parameters: domain, app, otp  
Return: 200 - Provisioning started  
        401 - Wrong OTP  
        400 - Domain Not available  
        500 - System error  

**API: /statusdomain**  
Verb: GET  
Input parameters: domain, app, otp  
Return: 200 - Status - One of CREATE_COMPANY, ASSIGN_APP, CREATE_ADMIN_USER, CREATE_API_USER, SAVE_TOKENS, PROVISION_USER, CREATE_EMAIL_TEMPLATE, PROVISION_APP, DON<PERSON>, ERROR  
        401 - Wrong OTP  
        400 - Domain Not available  
        500 - System error  

Return value in body:  
```
{
    "result": true,
    "message": ""
}
```
if result: false then the message is the error message

**Deployment instructions**  

Set system properties:

- rounds.onboarding.apiuser.user = "ROUNDS_APIUSER"  
- rounds.onboarding.apiuser.authtoken = "token"  
- ump.system.url.local = "" // Default value http://localhost:8080  
- ump.system.company.root = "" // Default value is UNVIRED  
- app.onboarding.sa.authtoken = "" // SA users auth token in root company  
