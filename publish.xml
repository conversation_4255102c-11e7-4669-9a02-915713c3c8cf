<?xml version="1.0"?>
<project xmlns:ivy="antlib:org.apache.ivy.ant" name="FormsSetupPublish" default="uploadbox" basedir=".">
  <scriptdef name="substring" language="javascript"><attribute name="text"/><attribute name="start"/><attribute name="end"/><attribute name="property"/><![CDATA[
var text = attributes.get("text"); var start = attributes.get("start"); var end = attributes.get("end") || text.length(); project.setProperty(attributes.get("property"), text.substring(start, end));
]]></scriptdef>
  <scriptdef name="packageversion" language="javascript"><attribute name="text"/><attribute name="start"/><attribute name="end"/><attribute name="property"/><![CDATA[
var text = attributes.get("text"); var start = attributes.get("start"); var end = attributes.get("end") || text.length(); var newstring = text.substring(start, end); newstring = newstring.replace(/\./g,'~'); var split = newstring.split("~"); var first = +split[0]; first = first.toString(); var middle = +split[1]; middle = middle.toString(); var last = +split[2]; last = last.toString(); newstring = first + '.' + middle + '.' + last; project.setProperty(attributes.get("property"), newstring);
]]></scriptdef>

  <target name="uploadbox">
    <property environment="env"/>
    <!--  Release string to be written  -->
    <loadfile property="release.str" srcFile="BuildNo.txt" failonerror="true"> </loadfile>
    <packageversion text="${release.str}" start="2" property="release.num"/>
    <echo message="Using package version number : ${release.num}"/>
    <echo message="Copying to DropBox"/>
    <exec dir="${basedir}/target" executable="uploadtodropbox.bat" failonerror="true">
      <arg value="onboarding-${release.num}.war"/>
      <arg value="/Forms/trunk/Setup/onboarding.war"/>
    </exec>
  </target>
</project>
