Follow this guide to deploy Forms App and Admin: https://intranet.indience.in:8443/unvired-digital-forms/forms-setup/-/wikis/Forms-Deployment

__Forms Provisioning Installation__

1.  Configure the following <system-properties> in standalone-full.xml  
```
    <property name="ump.system.company.datafolder" value = "DATA FOLDER FOR COMPANIES"/>
    <property name="forms.redis.hosts" value="HOST:PORT"/>
    <property name="forms.redis.database" value="NUMBER"/>
    <property name="forms.redis.password" value="PASSWORD"/>
    <property name="ump.root.company" value="ROOT_NAME_HERE"/>
```
2.  If using Sentinel, set "forms.redis.hosts" to a comma separated list of sentinels and add the master property
```
    <property name="forms.redis.hosts" value="SENTINEL1:PORT,SENTINEL2:PORT,SENTINEL3:PORT"/>
    <property name="forms.redis.master" values="CLUSTER NAME"/>
```
3.  Deploy latest onboarding.war in standalone/deployments (name has to be onboarding.war)
4.  Access http(s)://umpurl/onboarding.  For e.g. https://sandbox.unvired.io/onboarding

__Steps to provision ROOT company__

1.  Make sure the encryption key is copied into standalone/configuration folder.  The property ump.system.encryption.p12 has to be set according to the following: https://intranet.indience.in:8443/unvired/ump5/-/wikis/system-properties  Command to generate:  
```
keytool -genseckey -alias <rootcompany> -keypass <password> -keyalg AES  -keysize 256  -keystore <keystore.p12> -storepass <password> -storetype PKCS12
```
2.  Set the P12 Password password in ME properties and RESTART UMP.
3.  Deploy the DIGITAL_FORMS App for root company. 
4.  Set up the digital_forms database (use liquibase) and configure the user/password in UMP.
5.  Make sure S2S and Web frontends are assigned
6.  In APP properties:  
    a. Allow App Assignment (if child companies are to be provisioned).  
    b. Create User Processor class - com.digital.forms.ump.src.CreateUserFromSAML.  
    c. Attachment processor class - com.digital.forms.gen.processors.AttachmentProcessor.  
    d. JWT Processor class - com.digital.forms.ump.src.UMPJWTTokenValidator.  
7.  Configure Email Settings. 
8.  Visit the onboarding URL.  It will auto detect if ROOT company is not setup.
9.  Enter the discovery URL details.
10.  Enter the first user to create in the root company.
11.  Complete and wait.  Once provisioning is done, success message is displayed.
12.  Perform a forgot password operation using Forms Admin app to set the password for initial user.
13.  Also open custom email templates in UMP and make sure all URLs are proper

__Steps to provision CHILD company__

1.  Make sure ROOT company is setup as explained above.
2.  Visit the onboarding URL.  It will auto detect ROOT company setup is done and will direct to create new company.
3.  Click on Provision Company.
4.  Enter domain name.  If domain already exists and DIGITAL_FORMS is not provisioned, will prompt for SA user login and password.
5.  If company does not exist proceeds to next screen to enter discovery details.
6.  Enter the discovery URL details.
7.  Enter the first user to create in the child company.
8.  Complete and wait.  Once provisioning is done, success message is displayed.
9.  Check email for setting password.





