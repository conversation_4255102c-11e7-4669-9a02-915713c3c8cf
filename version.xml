<?xml version="1.0"?>
<project xmlns:ivy="antlib:org.apache.ivy.ant" name="FormsSetup" default="writebuildno" basedir=".">
  <scriptdef name="substring" language="javascript"><attribute name="text"/><attribute name="start"/><attribute name="end"/><attribute name="property"/><![CDATA[
var text = attributes.get("text"); var start = attributes.get("start"); var end = attributes.get("end") || text.length(); project.setProperty(attributes.get("property"), text.substring(start, end));
]]></scriptdef>
  <scriptdef name="packageversion" language="javascript"><attribute name="text"/><attribute name="start"/><attribute name="end"/><attribute name="property"/><![CDATA[
var text = attributes.get("text"); var start = attributes.get("start"); var end = attributes.get("end") || text.length(); var newstring = text.substring(start, end); newstring = newstring.replace(/\./g,'~'); var split = newstring.split("~"); var first = +split[0]; first = first.toString(); var middle = +split[1]; middle = middle.toString(); var last = +split[2]; last = last.toString(); newstring = first + '.' + middle + '.' + last; project.setProperty(attributes.get("property"), newstring);
]]></scriptdef>

  <!-- Get the release number -->
  <target name="getbuildno">
    <property environment="env" />
    <java jar="c:/unibuild/BuildNo.jar" fork="true" failonerror="true" maxmemory="128m">
      <arg value="formssetup"/>
      <arg value="-r=true"/>
      <arg value="-n=true"/>
    </java>

    <!-- Now read into the build numberfile into release.str property -->
    <loadfile property="release.str"
      srcFile="BuildNo.txt" failonerror="true">
    </loadfile>

    <echo message="Using release number : ${release.str}"/>
  </target>

  <target name="writebuildno" depends="getbuildno">
    <property environment="env"/>
    <!--  Release string to be written  -->
    <loadfile property="release.str" srcFile="BuildNo.txt" failonerror="true"> </loadfile>
    <packageversion text="${release.str}" start="2" property="release.num"/>
    <echo message="Using package version number : ${release.num}"/>
    <echo message="Auto Version update of POM spec"/>
    <replace file="${basedir}/pom.xml" token="0.0.1" value="${release.num}"/>
    <replace file="${basedir}/pom.xml" token="@build-number@" value="${env.BUILD_NUMBER} - ${env.BUILD_TIMESTAMP}"/>
    <replace file="${basedir}/pom.xml" token="@build-url@" value="${env.BUILD_URL}"/>
    <replace file="${basedir}/pom.xml" token="@git-version@" value="${env.GIT_COMMIT}"/>
    <replace file="${basedir}/src/main/java/com/unvired/digital/forms/util/Version.java" token="@VERSION@" value="${release.num}"/>
    <replace file="${basedir}/src/main/java/com/unvired/digital/forms/util/Version.java" token="@BUILD@" value="${env.BUILD_NUMBER} - ${env.BUILD_TIMESTAMP}"/>    
    
  </target>
</project>
