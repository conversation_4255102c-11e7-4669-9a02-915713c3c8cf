package com.unvired.digital.forms.helper;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class UmpRestHelperTest {

    @Test
    public void testIsPlanActive_WithActivePlan() {
        // Create test data that matches the JSON structure you provided
        Map<String, Object> responseBody = new HashMap<>();
        
        // Create company object
        Map<String, Object> company = new HashMap<>();
        company.put("compName", "Unvired Inc");
        company.put("compAlias", "UNVIRED");
        company.put("customerType", "ROOT");
        
        // Create plan array
        List<Map<String, Object>> plans = new ArrayList<>();
        Map<String, Object> plan = new HashMap<>();
        plan.put("planName", "Unlimited");
        plan.put("planType", "NORMAL");
        plan.put("fromDate", "09/01/2016");
        plan.put("toDate", "12/31/2030");  // Future date
        plan.put("planStatus", "true");
        plans.add(plan);
        
        company.put("plan", plans);
        responseBody.put("company", company);
        
        // Create ResponseEntity
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        // Test the method
        UmpRestHelper umpHelper = new UmpRestHelper("http://test", "TEST", "admin", "token");
        boolean result = umpHelper.isPlanActive(responseEntity);
        
        assertTrue(result, "Plan should be active with future end date");
    }
    
    @Test
    public void testIsPlanActive_WithExpiredPlan() {
        // Create test data with expired plan
        Map<String, Object> responseBody = new HashMap<>();
        
        Map<String, Object> company = new HashMap<>();
        List<Map<String, Object>> plans = new ArrayList<>();
        Map<String, Object> plan = new HashMap<>();
        plan.put("planName", "Unlimited");
        plan.put("planType", "NORMAL");
        plan.put("fromDate", "09/01/2016");
        plan.put("toDate", "12/31/2020");  // Past date
        plan.put("planStatus", "true");
        plans.add(plan);
        
        company.put("plan", plans);
        responseBody.put("company", company);
        
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        UmpRestHelper umpHelper = new UmpRestHelper("http://test", "TEST", "admin", "token");
        boolean result = umpHelper.isPlanActive(responseEntity);
        
        assertFalse(result, "Plan should not be active with past end date");
    }
    
    @Test
    public void testIsPlanActive_WithInactivePlan() {
        // Create test data with inactive plan
        Map<String, Object> responseBody = new HashMap<>();
        
        Map<String, Object> company = new HashMap<>();
        List<Map<String, Object>> plans = new ArrayList<>();
        Map<String, Object> plan = new HashMap<>();
        plan.put("planName", "Unlimited");
        plan.put("planType", "NORMAL");
        plan.put("fromDate", "09/01/2016");
        plan.put("toDate", "12/31/2030");  // Future date
        plan.put("planStatus", "false");   // Inactive
        plans.add(plan);
        
        company.put("plan", plans);
        responseBody.put("company", company);
        
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        UmpRestHelper umpHelper = new UmpRestHelper("http://test", "TEST", "admin", "token");
        boolean result = umpHelper.isPlanActive(responseEntity);
        
        assertFalse(result, "Plan should not be active when planStatus is false");
    }
    
    @Test
    public void testIsPlanActive_WithNullResponse() {
        UmpRestHelper umpHelper = new UmpRestHelper("http://test", "TEST", "admin", "token");
        boolean result = umpHelper.isPlanActive(null);
        
        assertFalse(result, "Should return false for null response");
    }
    
    @Test
    public void testIsPlanActive_WithNoPlans() {
        Map<String, Object> responseBody = new HashMap<>();
        Map<String, Object> company = new HashMap<>();
        company.put("compName", "Test Company");
        // No plans array
        responseBody.put("company", company);
        
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        UmpRestHelper umpHelper = new UmpRestHelper("http://test", "TEST", "admin", "token");
        boolean result = umpHelper.isPlanActive(responseEntity);
        
        assertFalse(result, "Should return false when no plans exist");
    }
}
