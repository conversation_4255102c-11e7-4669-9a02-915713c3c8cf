<!DOCTYPE html>
<html xmlns:th="http://www.w3.org/1999/xhtml">
<style>
    body {font-family: Arial, Helvetica, sans-serif;}
    * {box-sizing: border-box;}

    /* Full-width input fields */
    input[type=text], input[type=zip] ,input[type=password] {
    	width: 50%;
     	padding: 15px;
      	margin: 5px 0 10px 0;
      	display: inline-block;
      	background: #f1f1f1;
      	border-radius: 25px;
    	border: 2px;
    }

    /* Add a background color when the inputs get focus */
    input[type=text]:focus, input[type=password]:focus, input[type=zip]:focus {
      background-color: #ddd;
      outline: none;
    }

    /* Set a style for all buttons */
    button {
    	background-color: #4CAF50;
      	color: white;
      	padding: 14px 20px;
      	margin: 5px 0 10px 0;
      	border-radius: 25px;
    	border: 2px;
      	cursor: pointer;
      	width: 100%;
      	opacity: 0.9;
    }

    button:hover {
      opacity:1;
    }

    /* Float cancel and signup buttons and add an equal width */
    .signupbtn {
      text-align: center;
      width: 50%;
    }

    /* Add padding to container elements */
    .container {
      padding: 16px;
    }

    /* The Modal (background) */
    .modal {
      display: none; /* Hidden by default */
      position: fixed; /* Stay in place */
      z-index: 1; /* Sit on top */
      left: 0;
      top: 0;
      width: 100%; /* Full width */
      height: 100%; /* Full height */
      overflow: auto; /* Enable scroll if needed */
      background-color: #474e5d;
      padding-top: 50px;
    }

    /* Modal Content/Box */
    .modal-content {
      background-color: #fefefe;
      margin: 5% auto 15% auto; /* 5% from the top, 15% from the bottom and centered */
      width: 50%; /* Could be more or less, depending on screen size */
    }

    /* Style the horizontal ruler */
    hr {
      border: 1px solid #f1f1f1;
      margin-bottom: 25px;
    }


    /* Clear floats */
    .clearfix::after {
      content: "";
      clear: both;
      display: table;
    }

    /* Change styles for signup button on extra small screens */
    @media screen and (max-width: 300px) {
      .signupbtn {
         width: 25%;
         align: center;
      }
    }

    div{
    text-align:center;
  }
</style>

<head>
  <title>Digital Forms Provisioning</title>
</head>

<body>
  <form class="modal-content" action="/onboarding/createcompany" method="post">
      <div class="container">
        <header>
          <h1>Digital Forms Provisioning</h1>
        </header>

          <input type="text" placeholder="Choose Domain" name="company" id="company" required>
          <input type="hidden" th:value="${key}" name="key" id="key" required>
          <div class="clearfix">
              <button type="submit" class="signupbtn">Continue</button>
          </div>
          <br><br>
          <label><b><span th:text="${error}"></span></b></label>
      </div>
  </form>
</body>
</html>