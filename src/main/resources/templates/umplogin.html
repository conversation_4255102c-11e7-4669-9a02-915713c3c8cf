<!DOCTYPE html>
<html xmlns:th="http://www.w3.org/1999/xhtml">
<style>
    body {font-family: Arial, Helvetica, sans-serif;}
    * {box-sizing: border-box;}

    /* Full-width input fields */
    input[type=text], input[type=zip] ,input[type=password] {
    	width: 50%;
     	padding: 15px;
      	margin: 5px 0 10px 0;
      	display: inline-block;
      	background: #f1f1f1;
      	border-radius: 25px;
    	border: 2px;
    }

    /* Add a background color when the inputs get focus */
    input[type=text]:focus, input[type=password]:focus, input[type=zip]:focus {
      background-color: #ddd;
      outline: none;
    }

    /* Set a style for all buttons */
    button {
    	background-color: #4CAF50;
      	color: white;
      	padding: 14px 20px;
      	margin: 5px 0 10px 0;
      	border-radius: 25px;
    	border: 2px;
      	cursor: pointer;
      	width: 100%;
      	opacity: 0.9;
    }

    button:hover {
      opacity:1;
    }

    /* Float cancel and signup buttons and add an equal width */
    .signupbtn {
      text-align: center;
      width: 50%;
    }

    /* Add padding to container elements */
    .container {
      padding: 16px;
    }

    /* The Modal (background) */
    .modal {
      display: none; /* Hidden by default */
      position: fixed; /* Stay in place */
      z-index: 1; /* Sit on top */
      left: 0;
      top: 0;
      width: 100%; /* Full width */
      height: 100%; /* Full height */
      overflow: auto; /* Enable scroll if needed */
      background-color: #474e5d;
      padding-top: 50px;
    }

    /* Modal Content/Box */
    .modal-content {
      background-color: #fefefe;
      margin: 5% auto 15% auto; /* 5% from the top, 15% from the bottom and centered */
      width: 50%; /* Could be more or less, depending on screen size */
    }

    /* Style the horizontal ruler */
    hr {
      border: 1px solid #f1f1f1;
      margin-bottom: 25px;
    }


    /* Clear floats */
    .clearfix::after {
      content: "";
      clear: both;
      display: table;
    }

    /* Change styles for signup button on extra small screens */
    @media screen and (max-width: 300px) {
      .signupbtn {
         width: 25%;
         align: center;
      }
    }

    select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0 !important;
  background: #f1f1f1;
  background-image: none;
  }
/* Remove IE arrow */
select::-ms-expand {
  display: none;
}
/* Custom Select */
.select {
  position: relative;
  display: flex;
  width: 20em;
  height: 3em;
  line-height: 3;
  background: #f1f1f1;
  overflow: hidden;
  border-radius: .25em;
}
select {
  flex: 1;
  width: 50%;
  height: 3em;
  border-radius: 25px;
    	border: 2px;
  padding: 0 .5em;
  color: darkgrey;
  cursor: pointer;
}
/* Arrow */
.select::after {
  content: '\25BC';
  position: absolute;
  top: 0;
  right: 0;
  padding: 0 1em;
  background: #34495e;
  cursor: pointer;
  pointer-events: none;
  -webkit-transition: .25s all ease;
  -o-transition: .25s all ease;
  transition: .25s all ease;
}
/* Transition */
.select:hover::after {
  color: #f39c12;
}

    div{
    text-align:center;
}


</style>
<head>
  <title>Digital Forms Provisioning</title>
</head>

<body>

  <script type="text/javascript">
    function ShowHideDiv() {
        var umptype = document.getElementById("umptype");
        if (umptype.value == "sandbox") {
          document.getElementById("umpUrl").value = "https://sandbox.unvired.io/UMP";
          document.getElementById("umpUrl").readOnly = true;
          document.getElementById("company").value = "UNVIRED";
          document.getElementById("company").readOnly = true;
        }
        else if (umptype.value == "production") {
          document.getElementById("umpUrl").value = "https://live.unvired.io/UMP";
          document.getElementById("umpUrl").readOnly = true;
          document.getElementById("company").value = "UNVIRED";
          document.getElementById("company").readOnly = true;
        } else {
          document.getElementById("umpUrl").value = "";
          document.getElementById("umpUrl").readOnly = false;
          document.getElementById("company").value = "";
          document.getElementById("company").readOnly = false;
        }
    }
    ShowHideDiv();
</script>


<form class="modal-content" action="/onboarding/umplogin" method="post">
    <div class="container">

      <header>
            <h1>Digital Forms Provisioning</h1>
            <h2>Enter UMP Root Company SA Credentials</h2>
        </header>

        <select name="umptype" id="umptype" onchange = "ShowHideDiv()" required>
          <option value="" disabled selected>Select UMP URL</option>
          <option value="sandbox">Sandbox</option>
          <option value="production">Production</option>
          <option value="custom">Custom</option>
        </select>

        <input type="text" placeholder="URL" name="umpUrl" id="umpUrl" required>
        <input type="text" placeholder="ROOT COMPANY" name="company" id="company" required>
        <input type="text" placeholder="username" name="username" id="username" required value="SA" readonly>
        <input type="password" placeholder="Password" name="password" id="password" required>

        <div class="clearfix">
            <button type="submit" class="signupbtn">Login</button>
        </div>

        <br><br>

        <label><b><span th:text="${error}"></span></b></label>

    </div>
</form>

</body>
</html>