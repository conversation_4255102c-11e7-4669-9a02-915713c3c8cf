<!DOCTYPE html>
<html xmlns:th="http://www.w3.org/1999/xhtml">

<style>
    body {font-family: Arial, Helvetica, sans-serif;}
    * {box-sizing: border-box;}
    
    /* Full-width input fields */
    input[type=text], input[type=zip] ,input[type=password] {
    	width: 50%;
     	padding: 15px;
      	margin: 5px 0 10px 0;
      	display: inline-block;
      	background: #f1f1f1;
      	border-radius: 25px;
    	border: 2px;
    }

    /* Add a background color when the inputs get focus */
    input[type=text]:focus, input[type=password]:focus, input[type=zip]:focus {
      background-color: #ddd;
      outline: none;
    }

    /* Set a style for all buttons */
    button {
    	background-color: #4CAF50;
      	color: white;
      	padding: 14px 20px;
      	margin: 5px 0 10px 0;
      	border-radius: 25px;
    	  border: 2px;
      	cursor: pointer;
      	width: 100%;
      	opacity: 0.9;
    }

    button:hover {
      opacity:1;
    }

    /* Float cancel and signup buttons and add an equal width */
    .signupbtn {
      text-align: center;
      width: 50%;
    }

    /* Add padding to container elements */
    .container {
      padding: 16px;
    }

    /* The Modal (background) */
    .modal {
      display: none; /* Hidden by default */
      position: fixed; /* Stay in place */
      z-index: 1; /* Sit on top */
      left: 0;
      top: 0;
      width: 100%; /* Full width */
      height: 100%; /* Full height */
      overflow: auto; /* Enable scroll if needed */
      background-color: #474e5d;
      padding-top: 50px;
    }

    /* Modal Content/Box */
    .modal-content {
      background-color: #fefefe;
      margin: 5% auto 15% auto; /* 5% from the top, 15% from the bottom and centered */
      width: 50%; /* Could be more or less, depending on screen size */
    }

    /* Style the horizontal ruler */
    hr {
      border: 1px solid #f1f1f1;
      margin-bottom: 25px;
    }


    /* Clear floats */
    .clearfix::after {
      content: "";
      clear: both;
      display: table;
    }

    /* Change styles for signup button on extra small screens */
    @media screen and (max-width: 300px) {
      .signupbtn {
         width: 25%;
         align: center;
      }
    }

    select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0 !important;
  background: #f1f1f1;
  background-image: none;
  }
/* Remove IE arrow */
select::-ms-expand {
  display: none;
}
/* Custom Select */
.select {
  position: relative;
  display: flex;
  width: 20em;
  height: 3em;
  line-height: 3;
  background: #f1f1f1;
  overflow: hidden;
  border-radius: .25em;
}
select {
  flex: 1;
  width: 50%;
  height: 3em;
  border-radius: 25px;
    	border: 2px;
  padding: 0 .5em;
  color: darkgrey;
  cursor: pointer;
}
/* Arrow */
.select::after {
  content: '\25BC';
  position: absolute;
  top: 0;
  right: 0;
  padding: 0 1em;
  background: #34495e;
  cursor: pointer;
  pointer-events: none;
  -webkit-transition: .25s all ease;
  -o-transition: .25s all ease;
  transition: .25s all ease;
}
/* Transition */
.select:hover::after {
  color: #f39c12;
}


    div{
    text-align:center;
  }


</style>

<head>
  <title>Digital Forms Provisioning</title>
</head>
<body>

  <script type="text/javascript">
    function ShowHideDiv() {
        var logintype = document.getElementById("logintype");
        var dvads = document.getElementById("dvads");
        dvads.style.display = logintype.value == "ads" ? "block" : "none";
        document.getElementById("adsport").required = logintype.value == "ads" ? true : false;
        document.getElementById("adsdomain").required = logintype.value == "ads" ? true : false;
        var dvsap = document.getElementById("dvsap");
        dvsap.style.display = logintype.value == "sap" ? "block" : "none";
        document.getElementById("sapport").required = logintype.value == "sap" ? true : false;
    }
    ShowHideDiv();
</script>

<form class="modal-content" action="/onboarding/discoveryservice" method="post">
    <div class="container">

        <header>
          <h1>Digital Forms Provisioning</h1>
          <h2>Enter Discovery Service Details</h2>
        </header>

        <input type="text" placeholder="TurboForms App Display Comment" name="formscomment" id="formscomment" th:value=${formscomment} required>
        <input type="text" placeholder="Forms Admin App URL" name="formsadminurl" id="formsadminurl" th:value=${formsadminurl} required>

        <input type="text" placeholder="Forms App URL" name="formsappurl" id="formsappurl" th:value=${formsappurl} required> 

        <select name="logintype" id="logintype" onchange = "ShowHideDiv()" required>
          <option value="" disabled selected>Select Login Type</option>
          <option value="email">Email</option>
          <option value="ads">Active Directory</option>
          <option value="sap">SAP</option>
          <option value="saml">SSO</option>
        </select>

        <div id="dvads" style="display: none">
          <input type="text" placeholder="UMP Port Name of ADS server" name="adsport" id="adsport">
          <input type="text" placeholder="ADS Domain eg ['UNVIRED','APPLE']" name="adsdomain" id="adsdomain">
        </div>
        <div id="dvsap" style="display: none">
        <input type="text" placeholder="UMP Port Name of SAP server" name="sapport" id="sapport">
          </div>
        <input type="hidden" th:value="${company}" name="company" id="company" required>

        <div class="clearfix">
            <button type="submit" class="signupbtn">Continue</button>
        </div>

        <br><br>

        <label><b><span th:text="${error}"></span></b></label>

    </div>
</form>

</body>
</html>