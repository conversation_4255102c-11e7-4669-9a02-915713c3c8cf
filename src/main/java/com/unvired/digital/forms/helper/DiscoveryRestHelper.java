package com.unvired.digital.forms.helper;

import com.unvired.digital.forms.util.Constants;
import com.unvired.digital.forms.util.CustomRestHelper;
import com.unvired.digital.forms.util.URLConstants;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.ResponseExtractor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;

public class DiscoveryRestHelper {

    private static String hostUrl;
    private String rootCompany;
    private String accessToken;

    public DiscoveryRestHelper(final String cmsUnviredUrl, final String rootCompany) {
        hostUrl = cmsUnviredUrl;
        this.rootCompany = rootCompany;
        this.accessToken = System.getProperty("forms.onboarding.authtoken", "");
    }

    // working
    public Map getDomainInfo(String domain) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\DISCOVERY", accessToken);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = hostUrl + URLConstants.getDomainInfo;
        url = url.replace(URLConstants.DOMAIN, domain);

        ResponseEntity<Map> response = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);
        if (response == null || response.getStatusCodeValue() != 200
                || response.getBody() == null || response.getBody().get("data") == null) {
            throw new Exception("failed to check domain\nResponse: " + response);
        }

        ArrayList data = ((ArrayList) response.getBody().get("data"));

        if (data.isEmpty()) {
            return null;
        }

        return ((Map) (data).get(0));


    }

    // working
    public Map createDomain(String domain) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\DISCOVERY", accessToken);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("status", "published");

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = hostUrl + URLConstants.createDomain;
        url = url.replace(URLConstants.DOMAIN, domain);

        ResponseEntity<Map> response = CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);

        if (response == null || response.getStatusCodeValue() != 200
                || response.getBody() == null || response.getBody().get("data") == null) {
            throw new Exception("Failed to create domain\nResponse: " + response);
        }

        return ((Map) response.getBody().get("data"));
    }

    public void deleteDomain(String domainId) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\DISCOVERY", accessToken);

        String url = hostUrl + URLConstants.deleteDomain;
        url = url.replace(URLConstants.DOMAIN, domainId);

        ClientHttpResponse response = CustomRestHelper.getRestTemplate().execute(url, HttpMethod.DELETE, null, new ResponseExtractor<ClientHttpResponse>() {
            @Override
            public ClientHttpResponse extractData(ClientHttpResponse clientHttpResponse) throws IOException {
                return clientHttpResponse;
            }
        });

        if (response.getStatusCode() != HttpStatus.OK && response.getStatusCode() != HttpStatus.NO_CONTENT) {
            throw new Exception("Unable to delete URL. " + response.getBody());
        }

    }


    //  Working
    public ArrayList getURLInfo(String domainId, String ump_url) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\DISCOVERY", accessToken);

        String url = hostUrl + URLConstants.getURLInfo;
        url = url.replace(URLConstants.DOMAIN, domainId);
        url += "?ump_url=" + ump_url;

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        ResponseEntity<Map> response = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);
        if (response == null || response.getStatusCodeValue() != 200
                || response.getBody() == null || response.getBody().get("data") == null) {
            throw new Exception("Failed to check URL\nResponse: " + response);
        }

        return ((ArrayList) response.getBody().get("data"));
    }

    public ArrayList createURL(String domainId, String comment, String umpUrl, String status, String type, String adminurl, String appurl, String logintype, String adsport, String adsdomain, String sapport, boolean defaulturl) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\DISCOVERY", accessToken);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        //  Mandatory parameters
        // UMP URL must be based on the sandbox or prductions URLs.  In otehr cases as is passed in
        if (adminurl.contains(Constants.ADMIN_CLOUD_URL)) {
            if (adminurl.contains("sbox")) {
                params.add("ump_url", "https://api.sbox.turboapps.io");
            }
            else {
                params.add("ump_url", "https://api.turboapps.io");
            }
        }
        else {
            params.add("ump_url", umpUrl);
        }
        params.add("ump_comment", comment);
        params.add("ump_type", type);
        params.add("admin_url", adminurl);
        params.add("app_url", appurl);
        params.add("login_type", logintype);
        params.add("use_as_default", defaulturl ? "1" : "0");
        if (logintype.equalsIgnoreCase("ads")) {
            params.add("login_port", adsport);
            params.add("login_adsdomain", adsdomain);
        }
        else if (logintype.equalsIgnoreCase("sap")) {
            params.add("login_port", sapport);
        }
        params.add("status", status);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = hostUrl + URLConstants.createURL;
        url = url.replace(URLConstants.DOMAIN, domainId);

        ResponseEntity<Map> response = CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);

        if (response == null || response.getStatusCodeValue() != 200
                || response.getBody() == null || response.getBody().get("data") == null) {
            throw new Exception("Failed to create URL\nResponse: " + response);
        }

        return (ArrayList) response.getBody().get("data");

    }

    public boolean deleteURL(String domainId, String umpurl) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\DISCOVERY", accessToken);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = hostUrl + URLConstants.deleteURL;
        url = url.replace(URLConstants.DOMAIN, domainId);
        url += "?ump_url=" + umpurl;

        ResponseEntity<Map> response = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.DELETE, request, Map.class);
        if (response.getStatusCode() != HttpStatus.OK && response.getStatusCode() != HttpStatus.NO_CONTENT) {
            throw new Exception("Unable to delete URL. " + response.getBody());
        }

        return true;
    }

}