package com.unvired.digital.forms.helper;

import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import com.unvired.digital.forms.model.CreateCompanyObject;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Repository
public class RedisRepository implements IRedisRepository {

    private RedisTemplate<String, CreateCompanyObject> redisTemplate;
    private HashOperations<String, String, CreateCompanyObject> hashOperations;

    public RedisRepository() {
    }

    @Autowired
    public RedisRepository(RedisTemplate<String, CreateCompanyObject> redisTemplate) {
        this.redisTemplate = redisTemplate;
        init();
    }

    @PostConstruct
    private void init() {
        this.hashOperations = redisTemplate.opsForHash();

        // Optional: print serializer info for verification
        log.info("KeySerializer: {}", redisTemplate.getKeySerializer().getClass().getName());
        log.info("HashKeySerializer: {}", redisTemplate.getHashKeySerializer().getClass().getName());
        log.info("HashValueSerializer: {}", redisTemplate.getHashValueSerializer().getClass().getName());
    }

    @Override
    public CreateCompanyObject getCompany(String key) {
        log.debug("Getting company from Redis: key={}, field={}", key, key);
        return hashOperations.get(key, key);
    }

    @Override
    public void updateCompany(CreateCompanyObject company) {
        if (company == null || company.getKEY() == null) {
            log.warn("Attempted to update null company or company with null KEY");
            return;
        }

        String key = company.getKEY();
        log.debug("Saving company to Redis: key={}, field={}", key, key);

        hashOperations.put(key, key, company);
        redisTemplate.expire(key, 7, TimeUnit.DAYS);
    }

    @Override
    public void deleteCompany(CreateCompanyObject company) {
        if (company == null || company.getKEY() == null) {
            log.warn("Attempted to delete null company or company with null KEY");
            return;
        }

        String key = company.getKEY();
        log.debug("Deleting company from Redis: key={}, field={}", key, key);

        hashOperations.delete(key, key);
    }

    @Override
    public void deleteCompany(String key) {
        log.debug("Deleting company from Redis by key: key={}, field={}", key, key);
        hashOperations.delete(key, key);
    }
}