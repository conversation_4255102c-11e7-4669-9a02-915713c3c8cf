package com.unvired.digital.forms.helper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.unvired.digital.forms.model.CreateCompanyObject;
import com.unvired.digital.forms.model.RegisterDomainObject;
import com.unvired.digital.forms.util.Constants;
import com.unvired.digital.forms.util.CustomRestHelper;
import com.unvired.digital.forms.util.URLConstants;
import com.unvired.digital.forms.util.Utils;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.binary.Base64;
import org.assertj.core.util.Strings;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;


@Slf4j
public class UmpRestHelper {

    private final static String APPLICATION_NAME = "DIGITAL_FORMS";

    private String umpUrl;
    private String rootCompany;
    private String adminUsername;
    private String authToken;

    private String sboxUmpUrl;
    private String sboxAuthToken;

    public static UmpRestHelper getInstance()
    {
        // Check with company API if the company is present in UMP
        UmpRestHelper umpHelper = new UmpRestHelper(); 

        // UMP details
        umpHelper.umpUrl = System.getProperty("ump.system.url.local", "http://localhost:8080");
        umpHelper.rootCompany = System.getProperty("ump.system.company.root", "UNVIRED");

        // Admin user/token
        umpHelper.adminUsername = "SA";
        umpHelper.authToken =  System.getProperty("app.onboarding.sa.authtoken", "");

        // For dsicovery need access to sandbox also from Live
        umpHelper.sboxUmpUrl = "https://sandbox.unvired.io/UMP";
        umpHelper.sboxAuthToken =  System.getProperty("app.onboarding.sa.sbox.authtoken", "");
        
        // Check if it ends with /UMP
        if (!umpHelper.umpUrl.endsWith("UMP"))
            umpHelper.umpUrl += umpHelper.umpUrl.endsWith("/") ? "UMP" : "/UMP";

        return umpHelper;
    }
    
    // private constructor to use above in getInstance()
    private UmpRestHelper() {
    }

    public UmpRestHelper(String umpUrl, String rootCompany, String adminUsername, String authToken) {
        this.umpUrl = umpUrl;
        this.rootCompany = rootCompany;
        this.adminUsername = adminUsername;
        this.authToken = authToken;
    }

    public String getUmpUrl()
    {
        return umpUrl;
    }

    public String getRootCompany()
    {
        return rootCompany;
    }

    public String getAdminUsername()
    {
        return adminUsername;
    }

    public String getAuthToken()
    {
        return authToken;
    }
    
    public ResponseEntity<Map> getStatus(String company, String password) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBasicAuth(company + "\\" + Constants.ADMIN_USER, password);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLConstants.umpGetStatus;

        return CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);
    }

    // working
    public ResponseEntity<Map> getCompany(String company, String password) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBasicAuth(company + "\\" + Constants.ADMIN_USER, password);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLConstants.umpGetCompany;
        url = url.replace(URLConstants.COMPANY, company);

        return CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);
    }

    public ResponseEntity<Map> getApplication() {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBasicAuth(rootCompany + "\\" + Constants.ADMIN_USER, authToken);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);
        String url = umpUrl + URLConstants.umpApplication;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);

        return CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);
    }

    // working
    public boolean isCompanyExists(String company) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\" + adminUsername, authToken);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLConstants.umpGetCompany;
        url = url.replace(URLConstants.COMPANY, company);

        ResponseEntity<Map> response = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);

        return response.getStatusCode() == HttpStatus.OK;
    }

    public JsonObject discoverCompany(String company) {

        final JsonObject jsonResult = new JsonObject();
        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\" + adminUsername, sboxAuthToken);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        // First check in sandbox
        String url = sboxUmpUrl + URLConstants.umpGetCompany;
        url = url.replace(URLConstants.COMPANY, company);

        ResponseEntity<Map> response = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);
        if (response.getStatusCode() == HttpStatus.OK) {
            // Do we have the company existing?
        }

        return jsonResult;
    }    

    // working
    public void createCompany(Object compObj, String password) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\" + adminUsername, authToken);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        // Mandatory parameters
        params.add("address", "Fanin Suite 340");
        params.add("city", "Houston");
        params.add("state", "TX");
        params.add("zipcode", "77002");
        params.add("country", "United States");
        
        if (compObj instanceof CreateCompanyObject) {
            CreateCompanyObject company = (CreateCompanyObject) compObj;
            params.add("password", password);
            params.add("company", company.getCompany().toUpperCase());
            params.add("companyName", company.getCompany());
            params.add("email", ("forms+sa+" + company.getCompany() + "@unvired.io").toLowerCase());
        } else {
            RegisterDomainObject company = (RegisterDomainObject) compObj;
            params.add("password", password);
            params.add("company", company.getDomain().toUpperCase());
            params.add("companyName", company.getDomain());
            params.add("email", (company.getAppName().toLowerCase() + "+sa+" + ((RegisterDomainObject) company).getDomain() + "@unvired.io").toLowerCase());
        }

        // We will not add folder, let it default to storage on UMP

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
        String url = umpUrl + URLConstants.umpCreateCompany;
        ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);
        if (!responseEntity.getStatusCode().toString().startsWith("2")) {
            throw new Exception("Something went wrong. Please try again later");
        }
    }

    public void createEmailTemplate(String company, String templateName, String subject, String htmlTemplate, String description) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\" + adminUsername, authToken);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        // Mandatory parameters
        params.add("subject", subject);
        params.add("description", description);
        params.add("htmlContent", new String(Base64.encodeBase64(htmlTemplate.getBytes("UTF-8"))));

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLConstants.umpCreateEmailTemplate;
        url = url.replace(URLConstants.COMPANY, company);
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.TEMPLATE_NAME, templateName);

        ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);

        if (!responseEntity.getStatusCode().toString().startsWith("2")) {
            throw new Exception("Something went wrong. Please try again later");
        }
    }

    // working
    public void assignApp(final String appName, final String authUser, final String authPassword) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(authUser, authPassword);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLConstants.umpAppAssign;
        url = url.replace(URLConstants.APP_NAME, appName);

        ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);

        if (!responseEntity.getStatusCode().toString().startsWith("2")) {
            throw new Exception("Something went wrong. Please try again later");
        }

    }

    private ResponseEntity<Map> createUMPUser(final String authUser, final String authPassword,
            final MultiValueMap<String, Object> params) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(authUser, authPassword);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
        String url = umpUrl + URLConstants.umpCreateUser;

        return CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);
    }

    public boolean createUserInDB(final CreateCompanyObject company) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company.getCompany() + "\\" + Utils.createUserNameFromEmail(company.getEmail()),
                company.getCompanyPassword());

        final JsonObject inputMessageJson = new JsonObject();
        // Mandatory parameters
        inputMessageJson.addProperty("firstName", company.getFirstName());
        inputMessageJson.addProperty("lastName", company.getLastName());
        inputMessageJson.addProperty("email", company.getEmail().toLowerCase());
        inputMessageJson.addProperty("umpUserName", Utils.createUserNameFromEmail(company.getEmail()).toUpperCase());
        inputMessageJson.addProperty("role", "Admin");

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();

        params.add("inputMessage", inputMessageJson.toString());
        params.add("queuedExecute", false);
        params.add("messageFormat", "custom");

        ArrayList<Map<String, String>> frontEndList = getFrontEndIDs(APPLICATION_NAME, company.getCompany() + "\\" + Constants.ADMIN_USER,
                company.getCompanyPassword());
        String feWeb = Utils.getWebFrontEndParamName(frontEndList, Utils.createUserNameFromEmail(company.getEmail()));

        params.add("frontendUser", feWeb);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);

        String url = umpUrl + URLConstants.umpExecutePA;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.PA_NAME, Constants.PA_CREATE_USER_IN_EB);

        String response = CustomRestHelper.getRestTemplate().postForObject(url, request, String.class);

        response = response.substring(1, response.length() - 1);

        response = response.replace("\\\"", "\"");

        JsonObject responseJson = new JsonParser().parse(response).getAsJsonObject();

        return "Success".equalsIgnoreCase(responseJson.get("status").getAsString());
    }

    // working
    public ResponseEntity<Map> createFirstUser(final Object compObject, boolean useMailAsUser) throws Exception {

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("userType", "APPLICATION");
        params.add("strict", false);
        params.add("verifyEmail", false);
        
        
        if (compObject instanceof CreateCompanyObject) {
            CreateCompanyObject companyObject = (CreateCompanyObject) compObject;
            String createUser = companyObject.getEmail().toUpperCase();
            if (!useMailAsUser)
                createUser = Utils.createUserNameFromEmail(companyObject.getEmail());
            
            // Mandatory parameters
            params.add("application", APPLICATION_NAME);
            params.add("firstName", companyObject.getFirstName());
            params.add("lastName", companyObject.getLastName());
            params.add("mailId", companyObject.getEmail().toLowerCase());
            params.add("userName", createUser);
            params.add("password", companyObject.getCompanyPassword());// Set Standard password
            params.add("company", companyObject.getCompany());
    
            ArrayList<Map<String, String>> frontEndList = getFrontEndIDs(APPLICATION_NAME, 
                    companyObject.getCompany() + "\\" + Constants.ADMIN_USER, companyObject.getCompanyPassword());
            JsonArray frontEndUsers = Utils.getFrontEndParams(frontEndList,
                    Utils.createUserNameFromEmail(companyObject.getEmail()));
    
            params.add("frontendUsers", frontEndUsers);
    
            final HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setBasicAuth(companyObject.getCompany() + "\\" + Constants.ADMIN_USER, companyObject.getCompanyPassword());
            final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
    
            String url = umpUrl + URLConstants.umpCreateUserProvision;
            url = url.replace(URLConstants.USER_NAME, createUser);
    
            return CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);
        } else {
            RegisterDomainObject companyObject = (RegisterDomainObject) compObject;
            String createUser = companyObject.getEmail().toUpperCase();
            if (!useMailAsUser)
                createUser = Utils.createUserNameFromEmail(companyObject.getEmail());
            
            // Mandatory parameters
            params.add("application", companyObject.getAppName());
            params.add("firstName", companyObject.getFirstName());
            params.add("lastName", companyObject.getLastName());
            params.add("mailId", companyObject.getEmail().toLowerCase());
            params.add("userName", createUser);
            params.add("password", companyObject.getPassword());// Set to user selected password
            params.add("company", companyObject.getDomain());
    
            ArrayList<Map<String, String>> frontEndList = getFrontEndIDs(companyObject.getAppName(), 
                        companyObject.getRootCompany() + "\\" + companyObject.getRootUser(), 
                        companyObject.getRootPassword());
            JsonArray frontEndUsers = Utils.getFrontEndParams(frontEndList, Utils.createUserNameFromEmail(companyObject.getEmail()));
            params.add("frontendUsers", frontEndUsers);
    
            final HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setBasicAuth(companyObject.getRootCompany() + "\\" + companyObject.getRootUser(), companyObject.getRootPassword());
            final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
    
            String url = umpUrl + URLConstants.umpCreateUserProvision;
            url = url.replace(URLConstants.USER_NAME, createUser);
    
            return CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);
        }
    }

    // working. Get authKey from response
    public ResponseEntity<Map> createAdminUser(final Object companyObject) throws Exception {

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("strict", false);
        params.add("verifyEmail", false);
        params.add("userType", "ADMIN");
        
        if (companyObject instanceof CreateCompanyObject) {
            // Mandatory parameters
            CreateCompanyObject compObj =  (CreateCompanyObject) companyObject;
            params.add("firstName", "FORMS");
            params.add("lastName", "ADMIN");
            params.add("mailId", ("forms+" + compObj.getCompany() + "@unvired.io").toLowerCase());
            params.add("password", compObj.getCompanyPassword());// Set Standard password
            params.add("userName", "FORMSADMIN");
            params.add("company", compObj.getCompany());
            return createUMPUser(compObj.getCompany() + "\\" + Constants.ADMIN_USER, compObj.getCompanyPassword(), params);
        } else {
            // Mandatory parameters
            RegisterDomainObject compObj =  (RegisterDomainObject) companyObject;
            params.add("firstName", compObj.getAppName());
            params.add("lastName", "ADMIN");
            params.add("mailId", (compObj.getAppName().toLowerCase() + "+" + compObj.getDomain() + "@unvired.io").toLowerCase());
            params.add("password", compObj.getRandomPassword());// Set Standard password
            params.add("userName", compObj.getAppName() + "_ADMIN");
            params.add("company", compObj.getDomain());
            return createUMPUser(compObj.getRootCompany() + "\\" + compObj.getRootUser(), compObj.getRootPassword(), params);
        }
    }

    // working
    public ResponseEntity<Map> createApiUser(final Object compObject, final String authPassword)
            throws Exception {

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("strict", false);
        params.add("verifyEmail", false);
        params.add("userType", "APPLICATION");

        String userName = null;
        if (compObject instanceof CreateCompanyObject) {
            // Mandatory parameters
            CreateCompanyObject companyObject = (CreateCompanyObject) compObject;

            params.add("firstName", "API");
            params.add("lastName", "USER");
            userName = "APIUSER";
            params.add("userName", userName);
            params.add("application", APPLICATION_NAME);
            params.add("mailId", ("forms+apiuser+" + companyObject.getCompany().toLowerCase() + "@unvired.io").toLowerCase());
            params.add("password", companyObject.getCompanyPassword());// Set Standard password
            params.add("company", companyObject.getCompany());
    
            ArrayList<Map<String, String>> frontEndList = getFrontEndIDs(APPLICATION_NAME, 
                    companyObject.getCompany() + "\\" + Constants.ADMIN_USER, companyObject.getCompanyPassword());
            JsonObject webFE = Utils.getBrowserFrontEndParam(frontEndList, "APIUSER");
    
            JsonArray frontendUsers = new JsonArray();
            frontendUsers.add(webFE);
            params.add("frontendUsers", frontendUsers);
    
            final HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setBasicAuth(companyObject.getCompany() + "\\" + Constants.ADMIN_USER, authPassword);
    
            final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
            String url = umpUrl + URLConstants.umpCreateUserProvision;
            url = url.replace(URLConstants.USER_NAME, userName);
            return CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);
        } else {
            // Mandatory parameters
            RegisterDomainObject companyObject = (RegisterDomainObject) compObject;
            params.add("firstName", companyObject.getAppName());
            params.add("lastName", "APIUSER");  
            userName = companyObject.getAppName() + "_APIUSER";
            params.add("userName", userName);
            params.add("application", companyObject.getAppName());
            params.add("mailId", (companyObject.getAppName().toLowerCase() + "+apiuser+" + companyObject.getDomain().toLowerCase() + "@unvired.io").toLowerCase());
            params.add("password", companyObject.getRandomPassword());// Set Standard password
            params.add("company", companyObject.getDomain());
    
            ArrayList<Map<String, String>> frontEndList = getFrontEndIDs(companyObject.getAppName(), 
                                companyObject.getRootCompany() + "\\" + companyObject.getRootUser(), authPassword);
            JsonObject webFE = Utils.getBrowserFrontEndParam(frontEndList, userName);
    
            JsonArray frontendUsers = new JsonArray();
            frontendUsers.add(webFE);
            params.add("frontendUsers", frontendUsers);
    
            final HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setBasicAuth(companyObject.getRootCompany() + "\\" + companyObject.getRootUser(), authPassword);
            final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
    
            String url = umpUrl + URLConstants.umpCreateUserProvision;
            url = url.replace(URLConstants.USER_NAME, userName);
            return CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);
        }
    }

    public void deleteCompanyPA(final String company) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\" + adminUsername, authToken);

        final JsonObject inputMessageJson = new JsonObject();
        inputMessageJson.addProperty("companyName", company);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("inputMessage", inputMessageJson.toString());
        params.add("queuedExecute", false);
        params.add("messageFormat", "custom");

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);

        String url = umpUrl + URLConstants.umpExecutePA;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.PA_NAME, Constants.PA_DELETE_COMPANY);

        String response = CustomRestHelper.getRestTemplate().postForObject(url, request, String.class);
        if (response.contains("error")) {
            throw new Exception("unable to delete company: " + company);
        }
    }

    public void deleteCompany(final String company) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\" + adminUsername, authToken);

        String url = umpUrl + URLConstants.umpDeleteCompany;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.COMPANY, company);

        ClientHttpResponse response = CustomRestHelper.getRestTemplate().execute(url, HttpMethod.DELETE, new RequestCallback() {
            @Override
            public void doWithRequest(ClientHttpRequest clientHttpRequest) throws IOException {
                clientHttpRequest.getHeaders().putAll(headers);

            }
        }, new ResponseExtractor<ClientHttpResponse>() {
            @Override
            public ClientHttpResponse extractData(ClientHttpResponse clientHttpResponse) throws IOException {
                return clientHttpResponse;
            }
        });

        if (response.getStatusCode() != HttpStatus.OK && response.getStatusCode() != HttpStatus.NO_CONTENT) {
            log.info("Response Code {}", response.getStatusCode());
            throw new Exception("Failed to delete company. " + response.getBody());
        }

    }

    // Working Set APP Secret
    public void setAppSecret(final String company, final String password, final String key, final Object value)
            throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\" + Constants.ADMIN_USER, password);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("value", value);
        params.add("hidden", true);
        params.add("secure", true);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);

        String url = umpUrl + URLConstants.umpAppSecret;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.KEY, key);

        ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);

        if (!responseEntity.getStatusCode().toString().startsWith("2")) {
            throw new Exception("Unable to set App Secret: " + key + "=" + value);
        }
    }

    // Working
    public Map getUser(final String authUser, final String authPassword) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(authUser, authPassword);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLConstants.umpGetMeUser;

        final ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);

        if (responseEntity.getBody() != null && responseEntity.getBody().get("user") != null) {
            return (Map) responseEntity.getBody().get("user");
        }

        throw new Exception("failed to get User: " + authUser);
    }

    public Map getUser(final String userName) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(rootCompany + "\\" + adminUsername, authToken);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLConstants.umpGetUser;
        url = url.replace(URLConstants.USER_NAME, userName);

        final ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);

        if (responseEntity.getBody() != null && responseEntity.getBody().get("user") != null) {
            return (Map) responseEntity.getBody().get("user");
        }

        throw new Exception("failed to get User: ");
    }

    // Working
    public Object getAppSecret(final String key, final String company, final String password) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\" + Constants.ADMIN_USER, password);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLConstants.umpAppSecret;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.KEY, key);

        try {

            final ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request,
                    Map.class);

            if (responseEntity.getBody() != null && responseEntity.getBody().get("secrets") != null) {
                ArrayList<Map> settings = (ArrayList<Map>) responseEntity.getBody().get("secrets");

                for (Map setting : settings) {
                    if (!Strings.isNullOrEmpty(String.valueOf(setting.get("value")))
                            && !"null".equals(String.valueOf(setting.get("value")))) {
                        return setting.get("value");
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    // Testing App Settings
    public void setAppSetting(final String company, final String password, final String key, final Object value)
            throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\" + Constants.ADMIN_USER, password);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("value", value);
        params.add("hidden", true);
        params.add("secure", true);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);

        String url = umpUrl + URLConstants.umpAppSettings;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.KEY, key);

        ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().postForEntity(url, request, Map.class);

        if (!responseEntity.getStatusCode().toString().startsWith("2")) {
            throw new Exception("Unable to set App Setting: " + key + "=" + value);
        }
    }

    public boolean saveTokens(final CreateCompanyObject company, JsonObject inputMessageJson) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(
                company.getCompany() + "\\APIUSER"/* + Utils.createUserNameFromEmail(company.getEmail()) */,
                company.getCompanyPassword());

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("inputMessage", inputMessageJson.toString());
        params.add("queuedExecute", false);
        params.add("messageFormat", "custom");
        params.add("sensitive", true);

        ArrayList<Map<String, String>> frontEndList = getFrontEndIDs(APPLICATION_NAME, company.getCompany() + "\\" + Constants.ADMIN_USER,
                company.getCompanyPassword());
        String feWeb = Utils.getWebFrontEndParamName(frontEndList, "APIUSER");

        params.add("frontendUser", feWeb);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);

        String url = umpUrl + URLConstants.umpExecutePA;
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url.replace(URLConstants.PA_NAME, Constants.PA_SAVE_TOKEN);

        String response = CustomRestHelper.getRestTemplate().postForObject(url, request, String.class);

        response = response.substring(1, response.length() - 1);

        response = response.replace("\\\"", "\"");

        JsonObject responseJson = new JsonParser().parse(response).getAsJsonObject();

        return "Success".equalsIgnoreCase(responseJson.get("status").getAsString());
    }

    public void resetPassword(final CreateCompanyObject companyObject) throws Exception {

        final String user = Utils.createUserNameFromEmail(companyObject.getEmail());
        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(user, "");

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);

        String url = umpUrl + URLConstants.umpForgotPassword;
        url = url.replace(URLConstants.COMPANY, companyObject.getCompany());
        url = url.replace(URLConstants.USER_NAME, user);
        url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
        url = url + "FORGOT_PASSWORD_MAIL";

        // http://sandbox.unvired.io/UMP/API/v2/companies/UNVIRED/users/RAGHAV/forgotpassword
        // ?application=DIGITAL_FORMS&mailTemplate=FORGOT_PASSWORD_MAIL

        final ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);

        if (!responseEntity.getStatusCode().toString().startsWith("2")) {
            throw new Exception("Failed to Reset password for User: " + user);
        }

    }

    // public void resetPasswords(final CreateCompanyObject company) throws
    // Exception {
    //
    // final HttpHeaders headers = new HttpHeaders();
    // headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    // headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    // headers.setBasicAuth(company.getCompany() + "\\" +
    // Utils.createUserNameFromEmail(company.getEmail()),
    // Utils.getStandardPassword(company.getCompany()));
    //
    // final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String,
    // Object>();
    // params.add("frontendUser", Utils.createUserNameFromEmail(company.getEmail())
    // + "_Browser");
    // params.add("inputMessage", );
    // params.add("queuedExecute", false);
    // params.add("messageFormat", "custom");
    // params.add("sensitive", true);
    //
    //
    // final HttpEntity<MultiValueMap<String, Object>> request = new
    // HttpEntity<MultiValueMap<String, Object>>(params, headers);
    //
    // String url = umpUrl + URLConstants.umpExecutePA;
    // url = url.replace(URLConstants.APP_NAME, APPLICATION_NAME);
    // url = url.replace(URLConstants.PA_NAME, Constants.);
    //
    // String response = CustomRestHelper.getRestTemplate().postForObject(url, request,
    // String.class);
    //
    // response = response.substring(1, response.length() - 1);
    //
    // response = response.replace("\\\"", "\"");
    //
    // JsonObject responseJson = new JsonParser().parse(response).getAsJsonObject();
    //
    // if (!"Success".equalsIgnoreCase(responseJson.get("status").getAsString())) {
    // throw new Exception("Failed to Reset password\nError: " +
    // responseJson.get("error"));
    // }
    //
    // }

    public void createOrUpdateBackendUser(final String accessToken, final String userName,
            final CreateCompanyObject company) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company.getCompany() + "\\" + Utils.createUserNameFromEmail(company.getEmail()),
                company.getCompanyPassword());

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("backendUser", userName);
        params.add("password", accessToken);
        params.add("portName", "DIGITAL_FORMS_REST_SERVER_PORT");

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);

        String url = umpUrl + URLConstants.umpBackendUser;
        url = url.replace(URLConstants.USER_NAME, userName);

        // Update backend
        Map response = CustomRestHelper.getRestTemplate().postForObject(url, request, Map.class);
        if (response.get("error") != null || (!Strings.isNullOrEmpty(String.valueOf(response.get("error")))
                && !"null".equalsIgnoreCase(String.valueOf(response.get("error"))))) {
            throw new Exception(
                    "Failed to create backend for user : " + userName + "\n Error: " + response.get("error"));
        }
    }

    // Working
    private ArrayList<Map<String, String>> getFrontEndIDs(final String appName, final String authUser, final String autPassword) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(authUser, autPassword);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLConstants.umpApplication;
        url = url.replace(URLConstants.APP_NAME, appName);

        try {

            final ResponseEntity<Map> responseEntity = CustomRestHelper.getRestTemplate().exchange(url, HttpMethod.GET, request,
                    Map.class);

            if (responseEntity.getBody() != null && responseEntity.getBody().get("application") != null) {
                Map application = (Map) responseEntity.getBody().get("application");
                return (ArrayList<Map<String, String>>) application.get("frontends");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
    
    public boolean completeProvisioning(final RegisterDomainObject regInfo, String adminAuthKey, String apiAuthKey) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(regInfo.getDomain() + "\\" + regInfo.getEmail().toUpperCase(), regInfo.getPassword());

        final JsonObject inputMessageJson = new JsonObject();
        
        // Mandatory parameters
        inputMessageJson.addProperty("domain", regInfo.getDomain());
        inputMessageJson.addProperty("firstName", regInfo.getFirstName());
        inputMessageJson.addProperty("lastName", regInfo.getLastName());
        inputMessageJson.addProperty("email", regInfo.getEmail().toLowerCase());
        inputMessageJson.addProperty("umpUserName", regInfo.getEmail().toUpperCase());
        inputMessageJson.addProperty("umpUserPwd", regInfo.getRandomPassword());
        
        // Add the Admin auth key
        inputMessageJson.addProperty("adminUserName", regInfo.getAppName() + "_ADMIN");
        inputMessageJson.addProperty("adminAuthToken", adminAuthKey);

        // Add the API auth key
        inputMessageJson.addProperty("apiUserName", regInfo.getAppName() + "_APIUSER");
        inputMessageJson.addProperty("apiAuthToken", apiAuthKey);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();

        params.add("inputMessage", inputMessageJson.toString());
        params.add("queuedExecute", false);
        params.add("messageFormat", "custom");
        params.add("sensitive", true);      // Dont want to save auth tokens

        // We will execute as teh newly provisioned user using the web frontend
        ArrayList<Map<String, String>> frontEndList = getFrontEndIDs(regInfo.getAppName(), 
                    regInfo.getDomain() + "\\" + regInfo.getEmail().toUpperCase(),
                    regInfo.getPassword());
        String feWeb = Utils.getWebFrontEndParamName(frontEndList, Utils.createUserNameFromEmail(regInfo.getEmail()));
        params.add("frontendUser", feWeb);
        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLConstants.umpExecutePA;
        url = url.replace(URLConstants.APP_NAME, regInfo.getAppName());
        url = url.replace(URLConstants.PA_NAME, regInfo.getAppName() + "_PA_PROVISION_APP");

        String response = CustomRestHelper.getRestTemplate().postForObject(url, request, String.class);

        response = response.substring(1, response.length() - 1);
        response = response.replace("\\\"", "\"");
        JsonObject responseJson = new JsonParser().parse(response).getAsJsonObject();

        return "Success".equalsIgnoreCase(responseJson.get("status").getAsString());
    }    
    
    public void sendOTPMailPA(final RegisterDomainObject regInfo) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(regInfo.getRootCompany() + "\\" + regInfo.getApiUser(), regInfo.getApiToken());

        final JsonObject inputMessageJson = new JsonObject();
        inputMessageJson.addProperty("domain", regInfo.getDomain());
        inputMessageJson.addProperty("firstname", regInfo.getFirstName());
        inputMessageJson.addProperty("lastname", regInfo.getLastName());
        inputMessageJson.addProperty("email", regInfo.getEmail());
        inputMessageJson.addProperty("otp", regInfo.getOtp());
        
        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("inputMessage", new Gson().toJson(inputMessageJson));
        params.add("queuedExecute", false);
        params.add("messageFormat", "custom");

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLConstants.umpExecutePA;
        url = url.replace(URLConstants.APP_NAME, regInfo.getAppName());
        url = url.replace(URLConstants.PA_NAME, regInfo.getAppName() + "_PA_SEND_OTP");

        String response = CustomRestHelper.getRestTemplate().postForObject(url, request, String.class);
        if (response.contains("error")) {
            throw new Exception("Unable to send OTP mail: " + regInfo.getDomain());
        }
    }
}