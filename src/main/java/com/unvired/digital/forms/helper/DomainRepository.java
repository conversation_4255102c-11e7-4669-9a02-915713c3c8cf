package com.unvired.digital.forms.helper;

import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import com.unvired.digital.forms.model.RegisterDomainObject;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
public class DomainRepository implements IDomainRepository {

    private RedisTemplate<String, RegisterDomainObject> redisTemplate;
    private HashOperations<String, String, RegisterDomainObject> hashOperations;

    public DomainRepository() {
    }

    @Autowired
    public DomainRepository(RedisTemplate<String, RegisterDomainObject> redisTemplate) {
        this.redisTemplate = redisTemplate;
        init();
    }

    @PostConstruct
    private void init() {
        this.hashOperations = redisTemplate.opsForHash();

        log.info("RedisTemplate Key Serializer: {}", redisTemplate.getKeySerializer().getClass().getName());
        log.info("RedisTemplate HashKey Serializer: {}", redisTemplate.getHashKeySerializer().getClass().getName());
        log.info("RedisTemplate HashValue Serializer: {}", redisTemplate.getHashValueSerializer().getClass().getName());
    }

    @Override
    public RegisterDomainObject getDomain(String key) {
        log.debug("Getting domain object from Redis: key={}, field={}", key, key);
        return hashOperations.get(key, key);
    }

    @Override
    public void updateDomain(RegisterDomainObject company) {
        if (company == null || company.getKey() == null) {
            log.warn("Attempted to update a null company or company with null KEY");
            return;
        }

        String key = company.getKey();
        log.debug("Saving domain object to Redis: key={}, field={}", key, key);

        hashOperations.put(key, key, company);
        redisTemplate.expire(key, 1, TimeUnit.HOURS);
    }

    @Override
    public void deleteDomain(RegisterDomainObject company) {
        if (company == null || company.getKey() == null) {
            log.warn("Attempted to delete a null company or company with null KEY");
            return;
        }

        String key = company.getKey();
        log.debug("Deleting domain object from Redis: key={}, field={}", key, key);

        hashOperations.delete(key, key);
    }

    @Override
    public void deleteDomain(String key) {
        log.debug("Deleting domain object from Redis by key: key={}, field={}", key, key);
        hashOperations.delete(key, key);
    }
}