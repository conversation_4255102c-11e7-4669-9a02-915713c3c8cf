package com.unvired.digital.forms.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.LinkedHashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UMPUser {

    private String userName;
    private String firstName;
    private String lastName;
    private String mailId;
    private String password;
    private String userType;

    private String status;
    private String company;
    private boolean strict;
    private boolean verifyEmail;

    private Map<?,?>[] backendUsers;
    private Map<?,?>[] frontendIds;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMailId() {
        return mailId;
    }

    public void setMailId(String mailId) {
        this.mailId = mailId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Map[] getBackendUsers() {
        return backendUsers;
    }

    public void setBackendUsers(Map<?,?>[] backendUsers) {
        this.backendUsers = backendUsers;
    }

    public Map[] getFrontendIds() {
        return frontendIds;
    }

    public void setFrontendIds(Map<?,?>[] frontendIds) {
        this.frontendIds = frontendIds;
    }

    public JsonArray getFrontendIdsJson() {
        final JsonArray frontEndData = new JsonArray();

        for (Map<?,?> frontendId : frontendIds) {

            final JsonObject frontendIdJson = new JsonObject();
            frontendIdJson.addProperty("frontend", String.valueOf(frontendId.get("frontend")));
            frontendIdJson.addProperty("frontendType", String.valueOf(frontendId.get("frontendType")));
            frontendIdJson.addProperty("frontendUser", String.valueOf(frontendId.get("frontendUser")));

            frontEndData.add(frontendIdJson);
        }

        return frontEndData;
    }

    public void setFrontendIds(JsonArray frontEndData) {
        if (frontEndData.size() == 0) {
            return;
        }

        frontendIds = new Map[frontEndData.size()];

        for (int i = 0; i < frontEndData.size(); i++) {
            JsonObject frontendId = frontEndData.get(i).getAsJsonObject();

            final Map<String, String> feId = new LinkedHashMap<>();
            feId.put("frontend", String.valueOf(frontendId.get("frontend")));
            feId.put("frontendType", String.valueOf(frontendId.get("frontendType")));
            feId.put("frontendUser", String.valueOf(frontendId.get("frontendUser")));

            frontendIds[i] = feId;
        }

    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public boolean isStrict() {
        return strict;
    }

    public void setStrict(boolean strict) {
        this.strict = strict;
    }

    public boolean isVerifyEmail() {
        return verifyEmail;
    }

    public void setVerifyEmail(boolean verifyEmail) {
        this.verifyEmail = verifyEmail;
    }
}