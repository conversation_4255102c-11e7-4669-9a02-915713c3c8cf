package com.unvired.digital.forms.model;

import java.io.Serializable;
import org.springframework.data.redis.core.RedisHash;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
@RedisHash("RegisterDomainObject")
public class RegisterDomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    public enum ProvisionState {
        INITIAL,
        CREATE_COMPANY, 
        ASSIGN_APP, 
        CREATE_ADMIN_USER,
        CREATE_API_USER,
        SAVE_TOKENS,
        PROVISION_USER,
        CREATE_EMAIL_TEMPLATE,
        PROVISION_APP,
        DONE,
        ERROR
    }
    
    // Key to identify the object
    private String key;
    private ProvisionState state;
    
    private String umpUrl;
    private String rootCompany;
    private String rootUser;
    private String rootPassword;
    private String apiUser;
    private String apiToken;
    
    private String otp;
    private String appName;
    private String domain;
    private String firstName;
    private String lastName;
    private String email;
    private String phone;
    private String password;
    
    private String randomPassword;

    public RegisterDomainObject() {
    }

    public RegisterDomainObject(String key) {
        this.key = key;
        this.state = ProvisionState.INITIAL;
    }

    public String getKey() {
        return key;
    }
    
    public ProvisionState getState() {
        return state;
    }
    
    public void setState(ProvisionState state) {
        this.state = state;
    }

    public String getOtp()
    {
        return otp;
    }

    public void setOtp(String otp)
    {
        this.otp = otp;
    }

    public String getAppName()
    {
        return appName;
    }

    public void setAppName(String appName)
    {
        this.appName = appName;
    }

    public String getDomain()
    {
        return domain;
    }

    public void setDomain(String domain)
    {
        this.domain = domain;
    }
    

    public String getUmpUrl() {
        return umpUrl;
    }

    public void setUmpUrl(String umpUrl) {
        this.umpUrl = umpUrl;
    }

    public String getRootCompany() {
        return rootCompany;
    }

    public void setRootCompany(String rootCompany) {
        this.rootCompany = rootCompany;
    }

    public String getRootUser()
    {
        return rootUser;
    }

    public void setRootUser(String rootUser)
    {
        this.rootUser = rootUser;
    }

    public String getRootPassword() {
        return rootPassword;
    }

    public void setRootPassword(String authToken) {
        this.rootPassword = authToken;
    }

    public String getApiUser()
    {
        return apiUser;
    }

    public void setApiUser(String apiUser)
    {
        this.apiUser = apiUser;
    }

    public String getApiToken()
    {
        return apiToken;
    }

    public void setApiToken(String apiToken)
    {
        this.apiToken = apiToken;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getRandomPassword()
    {
        return randomPassword;
    }

    public void setRandomPassword(String randomPassword)
    {
        this.randomPassword = randomPassword;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }
}