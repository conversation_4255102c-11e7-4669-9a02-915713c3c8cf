package com.unvired.digital.forms.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.data.redis.core.RedisHash;

import javax.servlet.http.Cookie;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@RedisHash("CreateCompanyObject")
public class CreateCompanyObject implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    public enum CREATE_STATE {
        NONE, COMPANY_CREATED, APP_ASSIGNED, ADMIN_USER_CREATED, /* AUTH_TOKEN_SET, */
        API_USER_CREATED, /* API_AUTH_TOKEN_SET, */
        SAVE_TOKENS, UMP_FIRST_USER_CREATED, CREATE_USER_IN_DB, CREATE_API_USER_BACKEND, CREATE_APP_SECRET,
        CREATE_EMAIL_TEMPLATE, ENABLE_DISCOVERY_OF_URLS
    }

    // private long createTime;
    private String KEY;

    private String umptype;
    private String umpUrl;
    private String installationType;

    private String rootCompany;
    private String rootPassword;

    private String firstName;
    private String lastName;

    private String email;
    private String company;
    private String companyPassword;

    private String formscomment;
    private String formsappurl;
    private String formsadminurl;
    private String logintype;
    private String sapport;
    private String adsport;
    private String adsdomain;

    private String profileId;

    private String adminResourceId;
    private String userResourceId;

    private String adminRoleId;
    private String userRoleId;

    private String passwordHash;

    private CREATE_STATE status;

    private Cookie cookie;

    public CreateCompanyObject() {
    }

    public CreateCompanyObject(String KEY) {
        this.KEY = KEY;
        setStatus(CREATE_STATE.NONE);
    }

    public String getUmptype() {
        return umptype;
    }

    public void setUmptype(String umptype) {
        this.umptype = umptype;
    }

    public String getInstallationType() {
        return installationType;
    }

    public void setInstallationType(String installationType) {
        this.installationType = installationType;
    }

    public String getAdsdomain() {
        return adsdomain;
    }

    public void setAdsdomain(String adsdomain) {
        this.adsdomain = adsdomain;
    }

    public String getAdsport() {
        return adsport;
    }

    public void setAdsport(String adsport) {
        this.adsport = adsport;
    }

    public String getSapport() {
        return sapport;
    }

    public void setSapport(String sapport) {
        this.sapport = sapport;
    }

    public String getLogintype() {
        return logintype;
    }

    public void setLogintype(String logintype) {
        this.logintype = logintype;
    }

    public String getFormsadminurl() {
        return formsadminurl;
    }

    public void setFormsadminurl(String formsadminurl) {
        this.formsadminurl = formsadminurl;
    }

    public String getFormsappurl() {
        return formsappurl;
    }

    public void setFormsappurl(String formsappurl) {
        this.formsappurl = formsappurl;
    }    

    public String getKEY() {
        return KEY;
    }

    public String getUmpUrl() {
        return umpUrl;
    }

    public void setUmpUrl(String umpUrl) {
        this.umpUrl = umpUrl;
    }

    public String getRootCompany() {
        return rootCompany;
    }

    public void setRootCompany(String rootCompany) {
        this.rootCompany = rootCompany;
    }

    public String getRootPassword() {
        return rootPassword;
    }

    public void setRootPassword(String authToken) {
        this.rootPassword = authToken;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

   /* public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }*/

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCompanyPassword() {
        return companyPassword;
    }

    public void setCompanyPassword(String companyPassword) {
        this.companyPassword = companyPassword;
    }

    public CREATE_STATE getStatus() {
        return status;
    }

    public void setStatus(CREATE_STATE status) {
        this.status = status;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getAdminResourceId() {
        return adminResourceId;
    }

    public void setAdminResourceId(String adminResourceId) {
        this.adminResourceId = adminResourceId;
    }

    public String getUserResourceId() {
        return userResourceId;
    }

    public void setUserResourceId(String userResourceId) {
        this.userResourceId = userResourceId;
    }

    public String getAdminRoleId() {
        return adminRoleId;
    }

    public void setAdminRoleId(String adminRoleId) {
        this.adminRoleId = adminRoleId;
    }

    public String getUserRoleId() {
        return userRoleId;
    }

    public void setUserRoleId(String userRoleId) {
        this.userRoleId = userRoleId;
    }

    public Cookie getCookie() {
        return cookie;
    }

    public void setCookie(Cookie cookie) {
        this.cookie = cookie;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }
    
    public String getFormscomment() {
        return formscomment;
    }

    public void setFormscomment(String formscomment) {
        this.formscomment = formscomment;
    }
}