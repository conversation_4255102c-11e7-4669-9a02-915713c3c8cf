package com.unvired.digital.forms.model;

import java.io.Serializable;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("session")
public class SessionObject implements Serializable {

    private String umptype;
    private String umpUrl;
    private String company;
    private String password;
    private String installationType;

    private String formscomment;
    private String formsappurl;
    private String formsadminurl;
    private String logintype;
    private String sapport;
    private String adsport;
    private String adsdomain;

    public String getAdsdomain() {
        return adsdomain;
    }

    public String getUmptype() {
        return umptype;
    }

    public void setUmptype(String umptype) {
        this.umptype = umptype;
    }

    public String getInstallationType() {
        return installationType;
    }

    public void setInstallationType(String installationType) {
        this.installationType = installationType;
    }

    public void setAdsdomain(String adsdomain) {
        this.adsdomain = adsdomain;
    }

    public String getAdsport() {
        return adsport;
    }

    public void setAdsport(String adsport) {
        this.adsport = adsport;
    }

    public String getSapport() {
        return sapport;
    }

    public void setSapport(String sapport) {
        this.sapport = sapport;
    }

    public String getLogintype() {
        return logintype;
    }

    public void setLogintype(String logintype) {
        this.logintype = logintype;
    }

    public String getFormsadminurl() {
        return formsadminurl;
    }

    public void setFormsadminurl(String formsadminurl) {
        this.formsadminurl = formsadminurl;
    }

    public String getFormsappurl() {
        return formsappurl;
    }

    public void setFormsappurl(String formsappurl) {
        this.formsappurl = formsappurl;
    }

    public String getUmpUrl() {
        return umpUrl;
    }

    public void setUmpUrl(String umpUrl) {
        this.umpUrl = umpUrl;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    public String getFormscomment() {
        return formscomment;
    }

    public void setFormscomment(String formscomment) {
        this.formscomment = formscomment;
    }
}