package com.unvired.digital.forms.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.unvired.digital.forms.model.CreateCompanyObject;
import com.unvired.digital.forms.model.RegisterDomainObject;
import com.unvired.digital.forms.model.SessionObject;
import org.assertj.core.util.Strings;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.FileCopyUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Map;
import java.util.Random;

public class Utils {

    public static boolean isNullOrEmpty(String string) {
        return string == null || string.isEmpty() || "null".equalsIgnoreCase(string);
    }

    public static JsonArray getFrontEndParams(ArrayList<Map<String, String>> frontEnds, String name) {
        final JsonArray frontEndData = new JsonArray();

        if (frontEnds != null && !frontEnds.isEmpty()) {
            for (Map<String, String> frontend : frontEnds) {
                final JsonObject frontEndsJson = new JsonObject();
                frontEndsJson.addProperty("frontend", frontend.get("frontend"));
                frontEndsJson.addProperty("frontendType", frontend.get("frontendType"));
                frontEndsJson.addProperty("frontendUser", name + "_" + frontend.get("name"));

                frontEndData.add(frontEndsJson);
            }
        }

        return frontEndData;
    }

    public static JsonObject getBrowserFrontEndParam(ArrayList<Map<String, String>> frontEnds, String name) {

        if (frontEnds != null && !frontEnds.isEmpty()) {
            for (Map<String, String> frontend : frontEnds) {

                if (frontend.get("frontendType").toLowerCase().equalsIgnoreCase("browser")) {
                    final JsonObject frontEndsJson = new JsonObject();
                    frontEndsJson.addProperty("frontend", frontend.get("frontend"));
                    frontEndsJson.addProperty("frontendType", frontend.get("frontendType"));
                    frontEndsJson.addProperty("frontendUser", name + "_" + frontend.get("name"));

                    return frontEndsJson;
                }
            }
        }

        return null;
    }

    public static String getWebFrontEndParamName(ArrayList<Map<String, String>> frontEnds, String name) {

        JsonObject param = getBrowserFrontEndParam(frontEnds, name);

        if (param != null && param.has("frontendUser")) {
            return param.get("frontendUser").getAsString();
        }

        return "";
    }

    public static String getStandardPassword(String company) {

        if (isNullOrEmpty(company)) {
            return generateRandomPassword();
        }

        String pswd = company.substring(0, 1).toUpperCase();
        pswd = pswd + company.substring(1).toLowerCase();
        pswd = pswd + (new Random().nextInt((999999 - 100000) + 1) + 100000);
        pswd = pswd + "*";

        return pswd;

    }

    public static String generateRandomPassword() {
        final String upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        final String lower = "abcdefghijklmnopqrstuvwxyz";
        final String digits = "0123456789";
        final String symbols = "*@&_";

        final Random random = new SecureRandom();

        final char[] buf = new char[16];

        for (int index = 0; index < 16; ++index) {

            if (index < 5) {
                //  Add 5 Uppercase letters
                buf[index] = upper.charAt(random.nextInt(upper.length()));
            } else if (index < 10) {
                //  Add 5 Lowercase letters
                buf[index] = lower.charAt(random.nextInt(lower.length()));
            } else if (index < 14) {
                //  Add 4 Digits
                buf[index] = digits.charAt(random.nextInt(digits.length()));
            } else {
                //  Add 2 Special charecters
                buf[index] = symbols.charAt(random.nextInt(symbols.length()));
            }
        }

        return new String(buf);
    }

    public static String createUserNameFromEmail(String email) {
        return email.replaceAll("@", "-").replaceAll("\\.", "-");

    }

    // Form IO Helper methods

    public static String getLoginJson(String email, String password) {
        JsonObject cred = new JsonObject();
        cred.addProperty("email", email);
        cred.addProperty("password", password);

        JsonObject data = new JsonObject();
        data.add("data", cred);

        return data.toString();
    }

    public static String getCreateResourceJson(String name, String roleId1, String roleId2) {

        final JsonObject emailComponent = new JsonObject();
        emailComponent.addProperty("type", "email");
        emailComponent.addProperty("persistent", true);
        emailComponent.addProperty("unique", false);
        emailComponent.addProperty("protected", false);
        emailComponent.addProperty("defaultValue", "");
        emailComponent.addProperty("suffix", "");
        emailComponent.addProperty("prefix", "");
        emailComponent.addProperty("placeholder", "Enter your email address");
        emailComponent.addProperty("key", "email");
        emailComponent.addProperty("label", "Email");
        emailComponent.addProperty("inputType", "email");
        emailComponent.addProperty("tableView", true);
        emailComponent.addProperty("input", true);


        final JsonObject passwordComponent = new JsonObject();
        passwordComponent.addProperty("type", "password");
        passwordComponent.addProperty("persistent", true);
        passwordComponent.addProperty("protected", true);
        passwordComponent.addProperty("suffix", "");
        passwordComponent.addProperty("prefix", "");
        passwordComponent.addProperty("placeholder", "Enter your email password");
        passwordComponent.addProperty("key", "password");
        passwordComponent.addProperty("label", "Password");
        passwordComponent.addProperty("inputType", "password");
        passwordComponent.addProperty("tableView", false);
        passwordComponent.addProperty("input", true);


        final JsonObject buttonComponent = new JsonObject();
        buttonComponent.addProperty("type", "button");
        buttonComponent.addProperty("theme", "primary");
        buttonComponent.addProperty("disableOnInvalid", true);
        buttonComponent.addProperty("action", "submit");
        buttonComponent.addProperty("block", true);
        buttonComponent.addProperty("rightIcon", "");
        buttonComponent.addProperty("leftIcon", "");
        buttonComponent.addProperty("size", "md");
        buttonComponent.addProperty("key", "submit");
        buttonComponent.addProperty("tableView", false);
        buttonComponent.addProperty("label", "Submit");
        buttonComponent.addProperty("input", true);

        JsonArray componentsArray = new JsonArray();
        componentsArray.add(emailComponent);
        componentsArray.add(passwordComponent);
        componentsArray.add(buttonComponent);

        final JsonArray roleArray = new JsonArray();
        roleArray.add(roleId1);

        if (!Strings.isNullOrEmpty(roleId2)){
            roleArray.add(roleId2);
        }

        final JsonObject create_all = new JsonObject();
        create_all.addProperty("type", "create_all");
        create_all.add("roles", roleArray);

        final JsonObject read_all = new JsonObject();
        read_all.addProperty("type", "read_all");
        read_all.add("roles", roleArray);

        final JsonObject update_all = new JsonObject();
        update_all.addProperty("type", "update_all");
        update_all.add("roles", roleArray);

        final JsonObject delete_all = new JsonObject();
        delete_all.addProperty("type", "delete_all");
        delete_all.add("roles", roleArray);

        final JsonObject create_own = new JsonObject();
        create_own.addProperty("type", "create_own");
        create_own.add("roles", roleArray);

        final JsonObject read_own = new JsonObject();
        read_own.addProperty("type", "read_own");
        read_own.add("roles", roleArray);

        final JsonObject update_own = new JsonObject();
        update_own.addProperty("type", "update_own");
        update_own.add("roles", roleArray);

        final JsonObject delete_own = new JsonObject();
        delete_own.addProperty("type", "delete_own");
        delete_own.add("roles", roleArray);


        JsonArray submissionAccessArray = new JsonArray();
        submissionAccessArray.add(create_all);
        submissionAccessArray.add(read_all);
        submissionAccessArray.add(update_all);
        submissionAccessArray.add(delete_all);
        submissionAccessArray.add(create_own);
        submissionAccessArray.add(read_own);
        submissionAccessArray.add(update_own);
        submissionAccessArray.add(delete_own);

        JsonObject body = new JsonObject();
        body.addProperty("type", "resource");
        body.addProperty("title", name);
        body.addProperty("name", name.toLowerCase());
        body.addProperty("path", name.toLowerCase());
        body.add("tags", new JsonArray());
        body.add("components", componentsArray);
        body.add("submissionAccess", submissionAccessArray);

        return body.toString();
    }

    public static String getAssignRolesToResourceJson(String roleId) {
        JsonArray method = new JsonArray();
        method.add("create");

        JsonArray handler = new JsonArray();
        handler.add("after");

        JsonObject settings = new JsonObject();
        settings.addProperty("association", "new");
        settings.addProperty("role", roleId);
        settings.addProperty("type", "add");

        JsonObject role = new JsonObject();
        role.addProperty("name", "role");
        role.addProperty("title", "Assign Role");
        role.add("method", method);
        role.add("handler", handler);
        role.addProperty("priority", 1);
        role.add("settings", settings);

        JsonObject data = new JsonObject();
        data.add("data", role);

        return data.toString();
    }

    public static JsonObject getActionJsonWithResource(Map form, String[] resources) throws Exception {

        String formString = new ObjectMapper().writeValueAsString(form);

        final JsonObject formJson = new JsonParser().parse(formString).getAsJsonObject();

        if (resources == null) {
            return formJson;
        }

        for (String id : resources) {
            formJson.getAsJsonObject("settings").getAsJsonArray("resources").add(id);
        }

        return formJson;
    }

    public static boolean isUMPAuthenticated(SessionObject sessionObject) {

        return !(Strings.isNullOrEmpty(sessionObject.getUmpUrl()) || Strings.isNullOrEmpty(sessionObject.getPassword()) || Strings.isNullOrEmpty(sessionObject.getCompany()));

    }

    public static String readResourceFile(String filename) {
        Resource resource = new ClassPathResource(filename);

        try {
            InputStream inputStream = resource.getInputStream();

            byte[] byteData = FileCopyUtils.copyToByteArray(inputStream);
            String data = new String(byteData, StandardCharsets.UTF_8);
            return data;
        } catch (Exception e) {
            e.printStackTrace();

        }

        return "";
    }

}
