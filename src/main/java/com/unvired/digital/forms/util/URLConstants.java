package com.unvired.digital.forms.util;

public class URLConstants {
    public final static String APP_NAME = "{appName}";
    public final static String USER_NAME = "{userName}";
    public final static String TEMPLATE_NAME = "{templateName}";
    public final static String KEY = "{key}";
    public final static String COMPANY = "{company}";
    public final static String PA_NAME = "{PA}";

    public final static String ACCESS_TOKEN = "{access_token}";
    public final static String URL = "{url}";
    public final static String DOMAIN = "{domain}";
    public final static String DOMAIN_ID = "{domain_id}";
    public final static String URL_ID = "{url_id}";

    public final static String umpCreateUser = "/API/v2/users";
    public final static String umpCreateUserProvision = "/API/v2/users/" + USER_NAME + "/provision";
    public final static String umpBackendUser = "/API/v2/users/" + USER_NAME + "/backendusers";

    public final static String umpApplication = "/API/v2/applications/" + APP_NAME;
    public final static String umpAppAssign = "/API/v2/applications/" + APP_NAME + "/assign";
    public final static String umpAppSecret = "/API/v2/applications/" + APP_NAME + "/secret/" + KEY;
    public final static String umpAppSettings = "/API/v2/applications/" + APP_NAME + "/settings/" + KEY;

    public final static String umpGetStatus = "/API/v2/status";
    public final static String umpCreateCompany = "/API/v2/companies";
    public final static String umpGetCompany = "/API/v2/companies/" + COMPANY;
    public final static String umpDeleteCompany = "/API/v2/companies/" + COMPANY;
    public final static String umpCreateEmailTemplate = "/API/v2/companies/" + COMPANY + "/applications/" + APP_NAME + "/emailtemplate/" + TEMPLATE_NAME;

    public final static String umpGetMeUser = "/API/v2/users/:me";
    public final static String umpGetUser = "/API/v2/users/"+USER_NAME;
    //    public final static String umpForgotPassword = "/API/v2/users/:me/forgotpassword";
    public final static String umpForgotPassword = " /API/v2/companies/" + COMPANY + "/users/" + USER_NAME + "/forgotpassword?application=" + APP_NAME + "&mailTemplate=";

    public final static String umpExecutePA = "/API/v2/applications/" + APP_NAME + "/execute/" + PA_NAME;

    public final static String RESOURCE_ID = "{resourceId}";
    public final static String RESOURCE_NAME = "{resourceName}";
    public final static String LOGIN_FORM_ID = "{loginFormId}";
    public final static String LOGIN_ACTION_ID = "{loginActionId}";

    public final static String formLogin = "/user/login"; // Get JWTToken and get form ID
    public final static String formCreateUser = "/" + RESOURCE_NAME + "/submission";//Create User

    public final static String formCreateResource = "/form";//Create resource
    public final static String formCreateRoles = "/role";//Create Roles
    public final static String formAssignRoles = "/form/" + RESOURCE_ID + "/action";//Create Roles && get Login action
    public final static String formUpdateLoginAction = "/form/" + LOGIN_FORM_ID + "/action/" + LOGIN_ACTION_ID;//Update Login Action to Add Employee


    //  Discovery URLs
    // public final static String getDomainInfo = "/digital_forms_domains?access_token=" + ACCESS_TOKEN + "&filter[domain][eq]=" + DOMAIN;
    // public final static String createDomain = "/digital_forms_domains?access_token=" + ACCESS_TOKEN;
    // public final static String deleteDomain = "/digital_forms_domains/" + DOMAIN_ID + "?access_token=" + ACCESS_TOKEN;

    // public final static String getURLInfo = "/digital_forms_urls?access_token=" + ACCESS_TOKEN + "&filter[domain][eq]=" + DOMAIN_ID + "&fields=id,ump_url";
    // public final static String getURLInfoFilter = "&filter[ump_url][eq]=" + URL;
    // public final static String createURL = "/digital_forms_urls?access_token=" + ACCESS_TOKEN;
    // public final static String deleteURL = "/digital_forms_urls/" + URL_ID + "?access_token=" + ACCESS_TOKEN;

    public final static String getDomainInfo = "/domain/" + DOMAIN + "/application/DIGITAL_FORMS";
    public final static String createDomain = "/domain/" + DOMAIN + "/application/DIGITAL_FORMS";
    public final static String deleteDomain = "/domain/" + DOMAIN + "/application/DIGITAL_FORMS";

    public final static String getURLInfo = "/domain/" + DOMAIN + "/application/DIGITAL_FORMS/url";
    public final static String createURL = "/domain/" + DOMAIN + "/application/DIGITAL_FORMS/url";
    public final static String deleteURL ="/domain/" + DOMAIN + "/application/DIGITAL_FORMS/url";

}