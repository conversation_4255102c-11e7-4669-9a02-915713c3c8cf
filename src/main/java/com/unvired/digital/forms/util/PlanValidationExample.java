package com.unvired.digital.forms.util;

import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.unvired.digital.forms.helper.UmpRestHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * Example class showing how to use the plan validation functionality
 */
@Slf4j
public class PlanValidationExample {

    /**
     * Example method showing how to check if a company has an active plan
     * 
     * @param company The company name
     * @param password The company password
     * @return true if the company has an active plan, false otherwise
     */
    public static boolean validateCompanyPlan(String company, String password) {
        try {
            // Get UMP helper instance
            final UmpRestHelper umpHelper = UmpRestHelper.getInstance();
            
            // Get company information from UMP
            ResponseEntity<Map> companyResponse = umpHelper.getCompany(company, password);
            
            if (companyResponse.getStatusCode() == HttpStatus.OK) {
                // Check if the company has an active plan
                boolean isActive = umpHelper.isPlanActive(companyResponse);
                
                if (isActive) {
                    log.info("Company {} has an active plan", company);
                    return true;
                } else {
                    log.warn("Company {} does not have an active plan or plan is expired", company);
                    return false;
                }
            } else {
                log.error("Failed to get company information for {}: {}", company, companyResponse.getStatusCode());
                return false;
            }
            
        } catch (Exception e) {
            log.error("Error validating plan for company {}: {}", company, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Example method showing how to validate plan before allowing access to a feature
     * 
     * @param company The company name
     * @param password The company password
     * @param featureName The name of the feature being accessed
     * @return true if access should be allowed, false otherwise
     */
    public static boolean validateFeatureAccess(String company, String password, String featureName) {
        log.info("Validating access to feature '{}' for company '{}'", featureName, company);
        
        boolean hasActivePlan = validateCompanyPlan(company, password);
        
        if (!hasActivePlan) {
            log.warn("Access denied to feature '{}' for company '{}' - no active plan", featureName, company);
            return false;
        }
        
        log.info("Access granted to feature '{}' for company '{}'", featureName, company);
        return true;
    }
    
    /**
     * Example showing how to use this in a controller method
     */
    public static void exampleControllerUsage() {
        /*
         * In your controller method, you can use it like this:
         * 
         * @PostMapping("/some-feature")
         * public ResponseEntity<String> someFeature(@RequestParam String company, @RequestParam String password) {
         *     
         *     // Validate that the company has an active plan
         *     if (!PlanValidationExample.validateFeatureAccess(company, password, "SomeFeature")) {
         *         return ResponseEntity.status(HttpStatus.FORBIDDEN)
         *             .body(jsonResult("Access denied - no active plan", false));
         *     }
         *     
         *     // Continue with feature logic...
         *     return ResponseEntity.ok(jsonResult("Feature executed successfully", true));
         * }
         */
    }
    
    /**
     * Example showing direct usage of the isPlanActive method
     */
    public static void exampleDirectUsage() {
        /*
         * Direct usage example:
         * 
         * try {
         *     UmpRestHelper umpHelper = UmpRestHelper.getInstance();
         *     ResponseEntity<Map> response = umpHelper.getCompany("UNVIRED", "password");
         *     
         *     if (response.getStatusCode() == HttpStatus.OK) {
         *         boolean isActive = umpHelper.isPlanActive(response);
         *         
         *         if (isActive) {
         *             // Plan is active - proceed with operation
         *             log.info("Plan is active, proceeding...");
         *         } else {
         *             // Plan is not active or expired
         *             log.warn("Plan is not active or expired");
         *         }
         *     }
         * } catch (Exception e) {
         *     log.error("Error checking plan status", e);
         * }
         */
    }
}
