package com.unvired.digital.forms;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import com.unvired.digital.forms.model.CreateCompanyObject;
import com.unvired.digital.forms.model.RegisterDomainObject;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import lombok.extern.slf4j.Slf4j;
@Slf4j
@SpringBootApplication
public class DigitalFormsApplication extends SpringBootServletInitializer {

    private static final String REDIS_HOSTS = "forms.redis.hosts";
    private static final String REDIS_DB = "forms.redis.database";
    private static final String REDIS_PASSWORD = "forms.redis.password";
    private static final String REDIS_MASTER = "forms.redis.master";

    private static Class<DigitalFormsApplication> applicationClass = DigitalFormsApplication.class;

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(DigitalFormsApplication.class);
        app.setDefaultProperties(Collections.singletonMap("server.port", "8083"));
        app.run(args); 
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }

    @Bean
    JedisConnectionFactory jedisConnectionFactory() {
        String master = System.getProperty(REDIS_MASTER, "");
        String password = System.getProperty(REDIS_PASSWORD, "");
        String database = System.getProperty(REDIS_DB, "0");
        int dbNumber = 0;

        log.info("Initializing Redis");
        try {
            dbNumber = Integer.parseInt(database);
        } catch (Exception e) { /* Do nothing */ }
    
        if (master.isEmpty()) {                // Standalone
            String host = System.getProperty(REDIS_HOSTS, "localhost:6379");
            String[] hosts = host.split(",");
            String[] hostport = hosts[0].split(":");
            int port = 6379;
            try {
                port = Integer.parseInt(hostport[1]);
            } catch (Exception e) { /* Do nothing */}

            RedisStandaloneConfiguration standaloneConfiguration = new RedisStandaloneConfiguration(hostport[0], port);
            standaloneConfiguration.setPassword(RedisPassword.of(password));
            standaloneConfiguration.setDatabase(dbNumber);
            return new JedisConnectionFactory(standaloneConfiguration);
        } else {                // Sentinel
            String host = System.getProperty(REDIS_HOSTS, "localhost:26379");
            String[] hosts = host.split(",");
            Set<String> sentinels = new HashSet<>();
            for (String each: hosts)
                sentinels.add(each);
            RedisSentinelConfiguration sentinelConfiguration = new RedisSentinelConfiguration(master, sentinels);
            sentinelConfiguration.setPassword(RedisPassword.of(password));
            sentinelConfiguration.setDatabase(dbNumber);
            return new JedisConnectionFactory(sentinelConfiguration);
        }
    } 

    @Bean
    public RedisTemplate<String, CreateCompanyObject> redisTemplate() {
        final RedisTemplate<String, CreateCompanyObject> template = new RedisTemplate<>();
        template.setConnectionFactory(jedisConnectionFactory());

        // Use JSON for values, String for keys
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.afterPropertiesSet();
        log.info("RedisTemplate CreateCompanyObject initialized");
        return template;
    }

    @Bean
    public RedisTemplate<String, RegisterDomainObject> registerTemplate() {
        final RedisTemplate<String, RegisterDomainObject> template = new RedisTemplate<>();
        template.setConnectionFactory(jedisConnectionFactory());

        // Use JSON for values, String for keys
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.afterPropertiesSet();
        log.info("RedisTemplate RegisterDomainObject initialized");
        return template;
}    
}