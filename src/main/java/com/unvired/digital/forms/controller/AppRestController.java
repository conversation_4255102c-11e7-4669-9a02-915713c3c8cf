package com.unvired.digital.forms.controller;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.unvired.digital.forms.helper.DiscoveryRestHelper;
import com.unvired.digital.forms.helper.DomainRepository;
import com.unvired.digital.forms.helper.RedisRepository;
import com.unvired.digital.forms.helper.UmpRestHelper;
import com.unvired.digital.forms.model.CreateCompanyObject;
import com.unvired.digital.forms.model.RegisterDomainObject;
import com.unvired.digital.forms.model.RegisterDomainObject.ProvisionState;
import com.unvired.digital.forms.model.SessionObject;
import com.unvired.digital.forms.util.Constants;
import com.unvired.digital.forms.util.Utils;
import com.unvired.digital.forms.util.Version;

import lombok.extern.slf4j.Slf4j;
 
@Slf4j
@RestController
public class AppRestController {

    @Value("${apiToken}")
    private String apiToken;

    //  UMP Properties
    @Value("${umpUrl}")
    private String propertyUmpUrl;

    @Value("${rootCompany}")
    private String propertyRootCompany;

    @Value("${authToken}")
    private String propertyAuthToken;

    @Value("${cmsUnviredUrl}")
    private String cmsUnviredUrl;

    @Value("${accessTokenDiscovery}")
    private String accessTokenDiscovery;

    private final DomainRepository domainRepo;    
    private final RedisRepository redisRepository;
    
    @Autowired
    public AppRestController(RedisRepository redisRepository, DomainRepository domainRepo) {
        this.domainRepo = domainRepo;
        this.redisRepository = redisRepository;
    } 

    @CrossOrigin(origins="*")
    @GetMapping(value = "/validatedomain")
    public ResponseEntity<String> validatedomain(@RequestParam("domain") String domain)
    {
        // Determine the UMP URL.
        final UmpRestHelper umpHelper = UmpRestHelper.getInstance();

        ResponseEntity<String> result = null;
        try {
            // Check with company API if the company is present in UMP
            if (!umpHelper.isCompanyExists(domain.toUpperCase()))        // Company does not exist
                result = ResponseEntity.status(HttpStatus.OK).body(jsonResult("Valid domain: " + domain, true));
            else
                result = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonResult("Invalid domain", false));
        }
        catch (Exception e) {
            result = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(jsonResult("System Error: " + e.getMessage(), false));
        }

        return result;
    }

    @CrossOrigin(origins="*")
    @GetMapping(value = "/checkplanstatus")
    public ResponseEntity<String> checkPlanStatus(@RequestParam("company") String company, @RequestParam("password") String password)
    {
        final UmpRestHelper umpHelper = UmpRestHelper.getInstance();

        ResponseEntity<String> result = null;
        try {
            // Get company information
            ResponseEntity<Map> companyResponse = umpHelper.getCompany(company, password);

            if (companyResponse.getStatusCode() == HttpStatus.OK) {
                // Check if plan is active
                boolean isActive = umpHelper.isPlanActive(companyResponse);

                if (isActive) {
                    result = ResponseEntity.status(HttpStatus.OK).body(jsonResult("Plan is active", true));
                } else {
                    result = ResponseEntity.status(HttpStatus.OK).body(jsonResult("Plan is not active or expired", false));
                }
            } else {
                result = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonResult("Company not found", false));
            }
        }
        catch (Exception e) {
            result = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(jsonResult("System Error: " + e.getMessage(), false));
        }

        return result;
    }

    @CrossOrigin(origins="*")
    @GetMapping(value = "/discoverdomain")
    public ResponseEntity<String> discoverdomain(@RequestParam("domain") String domain)
    {
        // Determine the UMP URL.
        final UmpRestHelper umpHelper = UmpRestHelper.getInstance(); 

        ResponseEntity<String> result = null;
        try {
            // Check with company API if the company is present in UMP
            if (!umpHelper.isCompanyExists(domain.toUpperCase()))        // Company does not exist
                result = ResponseEntity.status(HttpStatus.OK).body(jsonResult("Valid domain: " + domain, true));
            else
                result = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonResult("Invalid domain", false));
        }
        catch (Exception e) {
            result = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(jsonResult("System Error: " + e.getMessage(), false));
        }
        
        return result;
    }
    
    @GetMapping(value = "/version")
    public String version() {
        return Version.getVersion();
    }    

    @CrossOrigin(origins="*")
    @PostMapping(value = "/registerdomain")
    public ResponseEntity<String> registerdomain(@RequestParam("domain") String domain, 
                @RequestParam("app") String app, 
                @RequestParam("firstname") String firstName,
                @RequestParam("lastname") String lastName, 
                @RequestParam("email") String email,
                @RequestParam("password") String password, 
                @RequestParam(value="phone", required=false, defaultValue="") String phone) 
    {
        // Determine the UMP URL.
        final UmpRestHelper umpHelper = UmpRestHelper.getInstance(); 

        // The key in redis for continuing the registration .. or if register is hit again
        String key = app + "_" + domain;
        key = key.toUpperCase();
        
        HttpStatus code = HttpStatus.OK;
        boolean success = true;
        String message = null;
        
        try {
            // Check with company API if the company is present in UMP
            if (!umpHelper.isCompanyExists(domain.toUpperCase())) {       // Company does not exist, register and proceed
                
                RegisterDomainObject regInfo = new RegisterDomainObject(key);
                // Save UMP Details
                regInfo.setUmpUrl(umpHelper.getUmpUrl());
                regInfo.setRootCompany(umpHelper.getRootCompany());
                regInfo.setRootUser(umpHelper.getAdminUsername());
                regInfo.setRootPassword(umpHelper.getAuthToken());

                // Save OTP for validation
                String otp = generateSecureOTP();
                regInfo.setOtp(otp);
                
                // Save domain details
                regInfo.setAppName(app.toUpperCase());
                regInfo.setDomain(domain.toUpperCase());
                regInfo.setEmail(email);
                regInfo.setFirstName(firstName);
                regInfo.setLastName(lastName);
                regInfo.setPassword(password);
                regInfo.setPhone(phone);
                
                // API user of the root company
                regInfo.setApiUser(System.getProperty(app.toLowerCase() + ".onboarding.apiuser.user", ""));
                regInfo.setApiToken(System.getProperty(app.toLowerCase() + ".onboarding.apiuser.authtoken", ""));
                
                // Save state
                domainRepo.updateDomain(regInfo);
                
                // Send the email
                try {
                    umpHelper.sendOTPMailPA(regInfo);
                } catch (Exception e) {
                    // Ignore for now
                    message = "Code: " + otp;
                } finally {
                    if (message == null)
                        message = "Check email and verify code";
                }
            } else {
                code = HttpStatus.BAD_REQUEST;
                message = "Invalid domain";
                success = false;
            }
        }
        catch (Exception e) {
            code = HttpStatus.INTERNAL_SERVER_ERROR;
            message = "System Error: " + e.getMessage();
            success = false;
        }
        
        return ResponseEntity.status(code).body(jsonResult(message, success));
    }
    
    @CrossOrigin(origins="*")
    @PostMapping(value = "/createdomain")
    public ResponseEntity<String> createdomain(@RequestParam("domain") String domain, 
                @RequestParam("app") String app, 
                @RequestParam(value="otp") String otp) 
    {
        ResponseEntity<String> result = null;
        
        // Determine the UMP URL.
        final UmpRestHelper umpHelper = UmpRestHelper.getInstance(); 

        // The key in redis for continuing the registration .. or if register is hit again
        String key = app + "_" + domain;
        key = key.toUpperCase();
        
        try {
            // Retrieve the saved state and continue
            final RegisterDomainObject regInfo = domainRepo.getDomain(key);
            if (regInfo != null) {
                if (otp.equals(regInfo.getOtp())) {
                    
                    // Start the creation
                    regInfo.setState(ProvisionState.CREATE_COMPANY);
                    
                    // Lets generate a random password and proceed to creation only if not already set
                    if (regInfo.getRandomPassword() == null || regInfo.getRandomPassword().isEmpty()) {
                        regInfo.setRandomPassword(Utils.getStandardPassword(regInfo.getDomain()));
                    }
                    domainRepo.updateDomain(regInfo);
                    
                    // Lets continue the registration
                    Executors.newSingleThreadExecutor().execute(new Runnable() {
                        @Override
                        public void run() 
                        {
                            // Kick off the creation
                            runProvisioning(umpHelper, regInfo);
                        }
                    });
                    result = ResponseEntity.status(HttpStatus.OK).body(jsonResult("We have started provisioning your domain and will send you an email when ready.", true));
                }
                else {
                    result = ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(jsonResult("Invalid OTP", false));
                }
            }
            else {
                result = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonResult("Invalid domain", false));
            }
        }
        catch (Exception e) {
            result = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(jsonResult("System Error: " + e.getMessage(), false));
        }
        finally {
            if (result == null)
                result = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonResult("Invalid domain", false));
        }
        
        return result;
    }
    
    @CrossOrigin(origins="*")
    @GetMapping(value = "/statusdomain")
    public ResponseEntity<String> statusdomain(@RequestParam("domain") String domain, 
                @RequestParam(value="app") String app, 
                @RequestParam(value="otp") String otp) 
    {
        ResponseEntity<String> result = null;
        
        // The key in redis for continuing the registration .. or if register is hit again
        String key = app + "_" + domain;
        
        try {
            // Retrieve the saved state and continue
            RegisterDomainObject regInfo = domainRepo.getDomain(key);
            if (regInfo != null) {
                if (otp.equals(regInfo.getOtp())) {
                    result = ResponseEntity.status(HttpStatus.OK).body(jsonResult(regInfo.getState().name(), true));
                }
                else {
                    result = ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(jsonResult("Invalid OTP", false));
                }
            }
            else {
                result = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonResult("Invalid domain", false));
            }
        }
        catch (Exception e) {
            result = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(jsonResult("System Error: " + e.getMessage(), false));
        }
        finally {
            if (result == null)
                result = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonResult("Invalid domain", false));
        }
        
        return result;
    }       
    
    /**
     * Regualar flow (No login)
     */
    @GetMapping(value = "/initiatesetup")
    public void initiatesetup(@RequestParam String apiToken, @RequestParam String firstname, @RequestParam String lastname, @RequestParam String email) {
        if (!this.apiToken.equals(apiToken)) {
            return;
        }

        final String redisKey = UUID.randomUUID().toString();
        final CreateCompanyObject company = new CreateCompanyObject(redisKey);
        company.setFirstName(firstname);
        company.setLastName(lastname);
        company.setEmail(email);

        redisRepository.updateCompany(company);

        //Initiate Email, send redisKey. API to be provided

    }

    @GetMapping({"/continuesetup", "onboarding/continuesetup"})
    public ModelAndView continuesetup(Model model, @RequestParam("key") String key) {

        final CreateCompanyObject company = redisRepository.getCompany(key);

        if (company == null) {
            return null;
        }

        model.addAttribute("firstname", company.getFirstName());
        model.addAttribute("lastname", company.getLastName());
        model.addAttribute("email", company.getEmail());
        model.addAttribute("key", key);

        String domain = company.getEmail().substring(company.getEmail().indexOf("@") + 1, company.getEmail().lastIndexOf("."));
        model.addAttribute("company", domain);

        return new ModelAndView("continuesetup");

    }

    @PostMapping("/continuesetup")
    public ModelAndView continuesetup(@RequestParam("company") String companyName, @RequestParam("key") String key) {

        CreateCompanyObject companyObject = redisRepository.getCompany(key);

        if (companyObject == null) {
            companyObject = redisRepository.getCompany(propertyUmpUrl + companyName);
        }

        if (companyObject == null) {

            ModelAndView view = new ModelAndView("continuesetup");
            view.addObject("error", "Sorry, Key expired. Try again\n");

            return view;
        }
        if (companyObject.getStatus() != CreateCompanyObject.CREATE_STATE.NONE) {
            return continueCompanyCreation(companyObject, false);
        }

        String firstname = companyObject.getFirstName();
        String lastname = companyObject.getLastName();
        String email = companyObject.getEmail();

        redisRepository.deleteCompany(key);

        companyObject = new CreateCompanyObject(propertyUmpUrl + companyName);

        companyObject.setFirstName(firstname);
        companyObject.setLastName(lastname);
        companyObject.setEmail(email);

        companyObject.setUmpUrl(propertyUmpUrl);
        companyObject.setRootCompany(propertyRootCompany);
        companyObject.setRootPassword(propertyAuthToken);

        companyObject.setCompany(companyName);
        redisRepository.updateCompany(companyObject);

        return continueCompanyCreation(companyObject, false);
    }

    @GetMapping({"/", "onboarding"})
    public ModelAndView startOnboarding(HttpServletRequest request, HttpSession session) {
        return new ModelAndView("redirect:umplogin");
    }

    @GetMapping({"/umplogin", "onboarding/umplogin"})
    public ModelAndView umplogin(HttpServletRequest request, HttpSession session) {

        log.info("Begin: onboarding/umplogin called");

        SessionObject sessionObject = (SessionObject) session.getAttribute("unviredSession");

        if (sessionObject != null) {
            ModelAndView nextView = handleLoggedInSession(sessionObject);
            if (nextView != null)
                return nextView;
        }

        return new ModelAndView("umplogin");
    }

    @PostMapping({ "/umplogin", "onboarding/umplogin" })
    public ModelAndView umploginValidate(HttpServletRequest request, HttpServletResponse response, @RequestParam("umptype") String umptype, @RequestParam("umpUrl") String umpUrl, @RequestParam("company") String companyName, @RequestParam("password") String password) {

        final UmpRestHelper umpRestHelper = new UmpRestHelper(umpUrl, companyName, Constants.ADMIN_USER, password);
        try {

            ResponseEntity<Map> responseEntity = umpRestHelper.getCompany(companyName, password);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                if ("ROOT".equals(((Map<?,?>) responseEntity.getBody().get("company")).get("customerType"))) {

                    ResponseEntity<Map> responseEntityStatus = umpRestHelper.getStatus(companyName, password);
                    if (responseEntityStatus.getStatusCode() == HttpStatus.OK) {

                        String install = (String) ((Map<?,?>) responseEntityStatus.getBody().get("umpResponse")).get("installationType");
                        SessionObject sessionObject = new SessionObject();
                        sessionObject.setUmptype(umptype);
                        sessionObject.setUmpUrl(umpUrl);
                        sessionObject.setPassword(password);
                        sessionObject.setCompany(companyName);
                        sessionObject.setInstallationType(install);
                        request.getSession().setAttribute("unviredSession", sessionObject);

                        // Handle situation where root company is not yet setup
                        ModelAndView nextView = handleLoggedInSession(sessionObject);
                        if (nextView != null)
                            return nextView;
            
                        return new ModelAndView("newcompany");
                    } else {
                        ModelAndView view = new ModelAndView("umplogin");
                        view.addObject("error", "Coould not retrieve UMP landscape type");
                        return view;
                    }
                } else {
                    ModelAndView view = new ModelAndView("umplogin");
                    view.addObject("error", "Not a root company");
                    return view;
                }
            } else {
                ModelAndView view = new ModelAndView("umplogin");
                view.addObject("error", "Invalid credentials");
                return view;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        ModelAndView view = new ModelAndView("umplogin");
        view.addObject("error", "Someting went wrong. Please try again later");
        return view;
    }

    @GetMapping({"/newcompany", "onboarding/newcompany"})
    public ModelAndView newcompany() {
        return  new ModelAndView("newcompany");
    }

    @GetMapping({"/createcompany", "onboarding/createcompany"})
    public ModelAndView createcompany(HttpServletRequest request) {
        return new ModelAndView("createcompany");
    }

    @PostMapping({"/createcompany", "onboarding/createcompany"})
    public ModelAndView createcompany(HttpServletRequest request, @RequestParam("company") String company) {

        SessionObject sessionObject = (SessionObject) request.getSession().getAttribute("unviredSession");

        final UmpRestHelper restHelper = new UmpRestHelper(sessionObject.getUmpUrl(), sessionObject.getCompany(), Constants.ADMIN_USER, sessionObject.getPassword());

        CreateCompanyObject companyObject = redisRepository.getCompany(sessionObject.getUmpUrl() + company);

        if (companyObject != null) {
            return continueCompanyCreation(companyObject, false);
        }

        if (restHelper.isCompanyExists(company)) {
            ModelAndView view = new ModelAndView("companysa");
            view.addObject("company", company);
            view.addObject("password", "NO_PASSWORD");
            return view;
        }

        ModelAndView view = new ModelAndView("discoveryservice");
        view.addObject("company", company);
        if (sessionObject.getUmptype().equalsIgnoreCase(("production"))) {
            view.addObject("formsappurl", "https://" + company.toLowerCase() + "." + Constants.APP_CLOUD_URL);
            view.addObject("formsadminurl", "https://" + company.toLowerCase() + "." + Constants.ADMIN_CLOUD_URL);
            view.addObject("formscomment", Constants.PROD_COMMENT);
        } else if (sessionObject.getUmptype().equalsIgnoreCase(("sandbox"))) {
            view.addObject("formsappurl", "https://" + company.toLowerCase() + ".sbox." + Constants.APP_CLOUD_URL);
            view.addObject("formsadminurl", "https://" + company.toLowerCase() + ".sbox." + Constants.ADMIN_CLOUD_URL);
            view.addObject("formscomment", Constants.SBOX_COMMENT);
        }
        else {
            view.addObject("formsappurl", "");
            view.addObject("formsadminurl", "");
            view.addObject("formscomment", "");
        }
        return view;
    }

    @GetMapping({"/companysa", "onboarding/companysa"})
    public ModelAndView companysa(HttpServletRequest request) {
        return new ModelAndView("companysa");
    }

    @PostMapping({"/companysa", "onboarding/companysa"})
    public ModelAndView companysa(HttpServletRequest request, @RequestParam("company") String company, @RequestParam("password") String password) {

        SessionObject sessionObject = (SessionObject) request.getSession().getAttribute("unviredSession");
        final UmpRestHelper restHelper = new UmpRestHelper(sessionObject.getUmpUrl(), sessionObject.getCompany(), Constants.ADMIN_USER, sessionObject.getPassword());
        ResponseEntity<Map> responseEntity = restHelper.getCompany(company, password);

        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            ModelAndView view = new ModelAndView("companysa");
            view.addObject("error", "Invalid password");
            view.addObject("company", company);
            return view;
        }

        ModelAndView view = new ModelAndView("discoveryservice");
        view.addObject("company", company);
        if (sessionObject.getUmptype().equalsIgnoreCase(("production"))) {
            view.addObject("formsappurl", "https://" + company.toLowerCase() + "." + Constants.APP_CLOUD_URL);
            view.addObject("formsadminurl", "https://" + company.toLowerCase() + "." + Constants.ADMIN_CLOUD_URL);
        } else if (sessionObject.getUmptype().equalsIgnoreCase(("sandbox"))) {
            view.addObject("formsappurl", "https://" + company.toLowerCase() + ".sbox." + Constants.APP_CLOUD_URL);
            view.addObject("formsadminurl", "https://" + company.toLowerCase() + ".sbox." + Constants.ADMIN_CLOUD_URL);
        }
        else {
            view.addObject("formsappurl", "");
            view.addObject("formsadminurl", "");
        }
        return view;
    }

    @GetMapping({"/deletecompany", "onboarding/deletecompany"})
    public ModelAndView deletecompany(HttpServletRequest request) {
        return new ModelAndView("deletecompany");
    }

    @PostMapping({"/deletecompany", "onboarding/deletecompany"})
    public ModelAndView deletecompany(HttpServletRequest request, @RequestParam("company") String company, @RequestParam("authtoken") String authToken) {

        SessionObject sessionObject = (SessionObject) request.getSession().getAttribute("unviredSession");
        final UmpRestHelper restHelper = new UmpRestHelper(sessionObject.getUmpUrl(), sessionObject.getCompany(), Constants.ADMIN_USER, sessionObject.getPassword());

        if (restHelper.isCompanyExists(company)) {
            return deleteCompany(sessionObject, company, authToken);
        } else {
            ModelAndView view = new ModelAndView("finalstatuspage");
            view.addObject("error", "Sorry, Company do not exist\n");
            return view;
        }
    }

    @GetMapping({"/createrootuser", "onboarding/createrootuser"})
    public ModelAndView createFirstUserRoot() {
        return new ModelAndView("createrootuser");
    }

    @PostMapping({"/createrootuser", "onboarding/createrootuser"})
    public ModelAndView createFirstUserRoot(HttpServletRequest request, @RequestParam("firstname") String firstname, @RequestParam("lastname") String lastname, @RequestParam("email") String email) {

        try {
            SessionObject sessionObject = (SessionObject) request.getSession().getAttribute("unviredSession");
            CreateCompanyObject companyObject = new CreateCompanyObject(sessionObject.getUmpUrl() + sessionObject.getCompany());
            companyObject.setUmptype(sessionObject.getUmptype());
            companyObject.setUmpUrl(sessionObject.getUmpUrl());
            companyObject.setCompanyPassword(sessionObject.getPassword());
            companyObject.setCompany(sessionObject.getCompany());
            companyObject.setRootPassword(sessionObject.getPassword());
            companyObject.setRootCompany(sessionObject.getCompany());
            companyObject.setFormscomment(sessionObject.getFormscomment());
            companyObject.setFormsadminurl(sessionObject.getFormsadminurl());
            companyObject.setFormsappurl(sessionObject.getFormsappurl());
            companyObject.setLogintype(sessionObject.getLogintype());
            companyObject.setAdsdomain(sessionObject.getAdsdomain());
            companyObject.setAdsport(sessionObject.getAdsport());
            companyObject.setSapport(sessionObject.getSapport());
            companyObject.setInstallationType(sessionObject.getInstallationType());

            companyObject.setFirstName(firstname);
            companyObject.setLastName(lastname);
            companyObject.setEmail(email);
            companyObject.setCompanyPassword(sessionObject.getPassword());
            redisRepository.updateCompany(companyObject);

            return setupRootCompany(companyObject);
        }
        catch (Exception e)
        {
            ModelAndView view = new ModelAndView("umplogin");
            view.addObject("error", "Something went wrong:" + e.getMessage());
            return view;
        }
    }

    @GetMapping({"/createcompanydetail", "onboarding/createcompanydetail"})
    public ModelAndView createcompanydetail(@RequestParam("company") String company) {
        ModelAndView view = new ModelAndView("createcompanydetail");
        view.addObject("company", company);
        return view;
    }

    @PostMapping({"/createcompanydetail", "onboarding/createcompanydetail"})
    public ModelAndView createcompanydetail(HttpServletRequest request, @RequestParam("company") String company, @RequestParam("firstname") String firstname, @RequestParam("lastname") String lastname, @RequestParam("email") String email, @RequestParam("password") String password) {

        SessionObject sessionObject = (SessionObject) request.getSession().getAttribute("unviredSession");
        final CreateCompanyObject companyObject = new CreateCompanyObject(sessionObject.getUmpUrl() + company);
        companyObject.setUmptype(sessionObject.getUmptype());
        companyObject.setUmpUrl(sessionObject.getUmpUrl());
        companyObject.setRootPassword(sessionObject.getPassword());
        companyObject.setRootCompany(sessionObject.getCompany());
        companyObject.setFormscomment(sessionObject.getFormscomment());
        companyObject.setFormsadminurl(sessionObject.getFormsadminurl());
        companyObject.setFormsappurl(sessionObject.getFormsappurl());
        companyObject.setLogintype(sessionObject.getLogintype());
        companyObject.setAdsdomain(sessionObject.getAdsdomain());
        companyObject.setAdsport(sessionObject.getAdsport());
        companyObject.setSapport(sessionObject.getSapport());
        companyObject.setInstallationType(sessionObject.getInstallationType());

        companyObject.setFirstName(firstname);
        companyObject.setLastName(lastname);
        companyObject.setEmail(email);
        companyObject.setCompany(company);

        if ("NO_PASSWORD".equals(password)) {
            companyObject.setCompanyPassword("");
            companyObject.setStatus(CreateCompanyObject.CREATE_STATE.NONE);
        } else {
            companyObject.setCompanyPassword(password);
            companyObject.setStatus(CreateCompanyObject.CREATE_STATE.COMPANY_CREATED);
        }

        redisRepository.updateCompany(companyObject);

        return continueCompanyCreation(companyObject, false);
    }

    @GetMapping({"/discoveryservice", "onboarding/discoveryservice"})
    public ModelAndView creatediscovery(@RequestParam("company") String company) {
        ModelAndView view = new ModelAndView("discoveryservice");
        view.addObject("company", company);
        return view;
    }

    @PostMapping({"/discoveryservice", "onboarding/discoveryservice"})
    public ModelAndView creatediscovery(HttpServletRequest request, @RequestParam("company") String company, 
        @RequestParam("formscomment") String formscomment,
        @RequestParam("formsadminurl") String formsadminurl,
        @RequestParam("formsappurl") String formsappurl, 
        @RequestParam("logintype") String logintype, 
        @RequestParam("adsport") String adsport,
        @RequestParam("adsdomain") String adsdomain,
        @RequestParam("sapport") String sapport) {

        try {
            SessionObject sessionObject = (SessionObject) request.getSession().getAttribute("unviredSession");
            sessionObject.setFormscomment(formscomment);
            sessionObject.setFormsadminurl(formsadminurl);
            sessionObject.setFormsappurl(formsappurl);
            sessionObject.setLogintype(logintype);
            sessionObject.setAdsdomain(adsdomain);
            sessionObject.setAdsport(adsport);
            sessionObject.setSapport(sapport);
            request.getSession().setAttribute("unviredSession", sessionObject);

            // If root company not setup lets do that
            if (!Boolean.valueOf(isRootSetup(sessionObject))) {
                return  new ModelAndView("createrootuser");
            }
            else {
                ModelAndView view = new ModelAndView("createcompanydetail");
                view.addObject("password", "NO_PASSWORD");
                view.addObject("company", company);
                return view;
            }
        }
        catch (Exception e)
        {
            ModelAndView view = new ModelAndView("umplogin");
            view.addObject("error", "Something went wrong:" + e.getMessage());
            return view;
        }

    }    

    private ModelAndView continueCompanyCreation(CreateCompanyObject companyObject, final boolean rootCompany) {
        log.info("Start of create company. Current state: " + companyObject.getStatus());
        try {
            final UmpRestHelper umpRestHelper = new UmpRestHelper(companyObject.getUmpUrl(), companyObject.getRootCompany(), Constants.ADMIN_USER, companyObject.getRootPassword());
            if (Strings.isNullOrEmpty(companyObject.getCompanyPassword())) {
                companyObject.setCompanyPassword(Utils.getStandardPassword(companyObject.getCompany()));
            }
         
            //  Create company in UMP
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.NONE) {
                if (!rootCompany) {
                    log.info("Creating company: {}", companyObject.getCompany());
                    umpRestHelper.createCompany(companyObject, companyObject.getCompanyPassword());
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.COMPANY_CREATED);
                redisRepository.updateCompany(companyObject);
                log.info("Company created");
            }

            //  Assign app in UMP
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.COMPANY_CREATED) {
                if (!rootCompany) {
                    log.info("Assigning App");
                    umpRestHelper.assignApp("DIGITAL_FORMS", companyObject.getCompany() + "\\" + Constants.ADMIN_USER, companyObject.getCompanyPassword());
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.APP_ASSIGNED);
                redisRepository.updateCompany(companyObject);
                log.info("App assigned");
            }

            //  Create Form Admin User in UMP
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.APP_ASSIGNED) {
                log.info("Creating FORMSADMIN");
                ResponseEntity<Map> responseEntity = umpRestHelper.createAdminUser(companyObject);

                if (!responseEntity.getStatusCode().toString().startsWith("2")) {
                    throw new Exception("Unable to create Form Admin user in ump");
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.ADMIN_USER_CREATED);
                redisRepository.updateCompany(companyObject);
                log.info("FORMSADMIN user created");
            }

            //  Create API User in UMP
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.ADMIN_USER_CREATED) {
                log.info("Creating APIUSER");
                ResponseEntity<Map> responseEntity = umpRestHelper.createApiUser(companyObject, companyObject.getCompanyPassword());

                if (!responseEntity.getStatusCode().toString().startsWith("2")) {
                    throw new Exception("Unable to create Form API user in ump");
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.API_USER_CREATED);
                redisRepository.updateCompany(companyObject);
                log.info("APIUSER created");
            }

            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.API_USER_CREATED) {
                log.info("Generating password hash");
                String pwdHash = BCrypt.gensalt();
                companyObject.setPasswordHash(pwdHash);

                JsonObject keys = new JsonObject();

                //  Generate Password Hash
                keys.addProperty("pwdHash", pwdHash);

                //  Get Admin Auth Key
                Map user = umpRestHelper.getUser(companyObject.getCompany() + "\\FORMSADMIN", companyObject.getCompanyPassword());
                String authKey = user.get("authKey").toString();

                if (Utils.isNullOrEmpty(authKey)) {
                    throw new Exception("FORMSADMIN Auth key not found");
                }

                keys.addProperty("adminAuthKey", authKey);

                //  get API User Auth Key
                user = umpRestHelper.getUser(companyObject.getCompany() + "\\APIUSER", companyObject.getCompanyPassword());
                authKey = user.get("authKey").toString();

                if (Utils.isNullOrEmpty(authKey)) {
                    throw new Exception("APIUSER Auth key not found");
                }

                keys.addProperty("apiAuthKey", authKey);

                log.info("Saving tokens");
                boolean saved = umpRestHelper.saveTokens(companyObject, keys);

                if (!saved) {
                    throw new Exception("Unable to Save tokens");
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.SAVE_TOKENS);
                redisRepository.updateCompany(companyObject);
                log.info("Tokens saved");
            }

            //  Create First User in UMP
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.SAVE_TOKENS) {
                log.info("Creating First user in UMP");

                ResponseEntity<Map> responseEntity = umpRestHelper.createFirstUser(companyObject, false);

                if (!responseEntity.getStatusCode().toString().startsWith("2")) {
                    throw new Exception("Unable to create Form First user in ump");
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.UMP_FIRST_USER_CREATED);
                redisRepository.updateCompany(companyObject);
                log.info("First user created");
            }

            //  Create User in DB
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.UMP_FIRST_USER_CREATED) {
                log.info("Creating user in DB");
                boolean created = umpRestHelper.createUserInDB(companyObject);

                if (!created) {
                    throw new Exception("Unable to create User in DB");
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.CREATE_USER_IN_DB);
                redisRepository.updateCompany(companyObject);
                log.info("User created");
            }

            //  Create App Secret
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.CREATE_USER_IN_DB) {
                if (rootCompany) {
                    log.info("Setting app secret for root setup");
                    umpRestHelper.setAppSecret(companyObject.getCompany() + "\\" + Constants.ADMIN_USER, companyObject.getCompanyPassword(), Constants.ROOT_SETUP, true);
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.CREATE_APP_SECRET);
                redisRepository.updateCompany(companyObject);
                log.info("Creating email temmplates enabled");
            }
            
            // Need to create teh email tempaltes here
            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.CREATE_APP_SECRET) {
                log.info("Creating email templates");
                
                createAppMailTemplate(umpRestHelper, companyObject);

                // Password reset has to be initiated after the templates are created
                if (!rootCompany) {
                    log.info("Initiating password reset for child company");
                    umpRestHelper.resetPassword(companyObject);
                }

                companyObject.setStatus(CreateCompanyObject.CREATE_STATE.CREATE_EMAIL_TEMPLATE);
                redisRepository.updateCompany(companyObject);
                log.info("Discovery of URLs enabled");
            }

            if (companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.CREATE_EMAIL_TEMPLATE) {
                log.info("Enable discovery of URLs");
                DiscoveryRestHelper discoveryRestHelper = new DiscoveryRestHelper(cmsUnviredUrl, companyObject.getRootCompany());

                boolean created = false;
                Map domainInfo = discoveryRestHelper.getDomainInfo(companyObject.getCompany());
                if (domainInfo == null) {
                    domainInfo = discoveryRestHelper.createDomain(companyObject.getCompany());
                    created = true;
                }

                ArrayList<?> urls = discoveryRestHelper.getURLInfo(companyObject.getCompany(), companyObject.getUmpUrl());

                if (urls == null || urls.isEmpty()) {
                    ArrayList<?> urlInfo = discoveryRestHelper.createURL(companyObject.getCompany(), companyObject.getFormscomment(), companyObject.getUmpUrl(), "published", 
                        companyObject.getInstallationType().substring(0,1), companyObject.getFormsadminurl(), companyObject.getFormsappurl(),
                        companyObject.getLogintype(), companyObject.getAdsport(), companyObject.getAdsdomain(), companyObject.getSapport(), created);
                    if (urlInfo == null || urlInfo.isEmpty()) {
                        // This is to rectified manually but can still be considered as complete
                        log.error("Discovery URLS could not be created.  This needs to be manually done");
                    }
                }

                // All done
                redisRepository.deleteCompany(companyObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Exception: " + e.getMessage());
            ModelAndView view = new ModelAndView("finalstatuspage");
            view.addObject("key", companyObject.getKEY());
            view.addObject("error", "Sorry, failed to complete. \n" + e.getMessage());

            return view;
        }

        ModelAndView view = new ModelAndView("finalstatuspage");
        view.addObject("message", "Domain provisioned successfully\n");

        return view;
    }

    private ModelAndView deleteCompany(SessionObject sessionObject, final String company, final String authToken) {
        log.info("Start of delete company. name: " + company);

        final UmpRestHelper restHelper = new UmpRestHelper(sessionObject.getUmpUrl(), sessionObject.getCompany(), Constants.ADMIN_USER, sessionObject.getPassword());
        final UmpRestHelper apiUserRestHelper = new UmpRestHelper(sessionObject.getUmpUrl(), sessionObject.getCompany(), Constants.API_USER, authToken);

        try {
            log.info("Executing DeleteCompany PA");
            apiUserRestHelper.deleteCompanyPA(company);
            log.info("PA Executed");

            log.info("Deleting company from UMP");
            restHelper.deleteCompany(company);
            log.info("Deleted from UMP");

            // Not deleting discovery for now
            
            // DiscoveryRestHelper discoveryRestHelper = new DiscoveryRestHelper(cmsUnviredUrl, accessTokenDiscovery);

            // log.info("Getting domain information for discovery");
            // Map domainInfo = discoveryRestHelper.getDomainInfo(company);
            // if (domainInfo != null) {

            //     log.info("Getting domain URLs for discovery");
            //     ArrayList urls = discoveryRestHelper.getURLInfo(domainInfo.get("id").toString(), null);

            //     if (urls == null || urls.isEmpty()) {
            //         throw new Exception("Domain entry not found to delete.");
            //     }

            //     for (Object urlInfo : urls) {
            //         String urlId = ((Map) urlInfo).get("id").toString();
            //         String url = ((Map) urlInfo).get("ump_url").toString();
            //         log.info("Deleting url:" + url + "\nurl id" + urlId);
            //         discoveryRestHelper.deleteURL(urlId);
            //         log.info("URL deleted");
            //     }

            //     log.info("Deleting domain info");
            //     discoveryRestHelper.deleteDomain(domainInfo.get("id").toString());
            //     log.info("Deleted");
            // }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("Exception: " + e.getMessage());
            ModelAndView view = new ModelAndView("finalstatuspage");
            view.addObject("error", "Sorry, failed to complete. \n" + e.getMessage());

            return view;
        }

        ModelAndView view = new ModelAndView("finalstatuspage");
        view.addObject("message", "Domain deleted successfully\n");

        return view;

    }

    private ModelAndView setupRootCompany(CreateCompanyObject companyObject) throws Exception {
        try {
            // Check if forms app is present?
            
            final UmpRestHelper restHelper = new UmpRestHelper(companyObject.getUmpUrl(), companyObject.getCompany(), Constants.ADMIN_USER, companyObject.getRootPassword());
            ResponseEntity<Map> app = restHelper.getApplication();
            if (app.getStatusCode() == HttpStatus.OK)
            {
                companyObject.setCompany(companyObject.getRootCompany());
                companyObject.setCompanyPassword(companyObject.getRootPassword());
                return continueCompanyCreation(companyObject, true);
            }
            else if (app.getStatusCode() == HttpStatus.NOT_FOUND)
            {
                throw new Exception("DIGITAL_FORMS app not present for root company.  Deploy and try again");    
            }
            else
            {
                throw new Exception("Could not retrieve DIGITAL_FORMS app for root company.  Please try again");    
            }
        }
        catch(Exception e) {
            throw new Exception("Something went wrong.  Please try again");
        }
    }

    private String isRootSetup(SessionObject sessionObject) throws Exception {
        final UmpRestHelper umpRestHelper = new UmpRestHelper(sessionObject.getUmpUrl(), sessionObject.getCompany(), Constants.ADMIN_USER, sessionObject.getPassword());
        return (String) umpRestHelper.getAppSecret(Constants.ROOT_SETUP, sessionObject.getCompany(), sessionObject.getPassword());
    }

    private void createAppMailTemplate(UmpRestHelper umpRestHelper, CreateCompanyObject companyObject) throws Exception {

        String umpURL = "";
        // UMP URL must be based on the sandbox or prductions URLs.  In otehr cases as is passed in
        if (companyObject.getFormsadminurl().contains(Constants.ADMIN_CLOUD_URL)) {
            if (companyObject.getFormsadminurl().contains("sbox"))
                umpURL = "https://api.sbox.turboapps.io";
            else
                umpURL = "https://api.turboapps.io";
        }
        else
            umpURL = companyObject.getUmpUrl();
        
        String template = Utils.readResourceFile("mailtemplates/" + Constants.FORGOT_PASSWORD_APP_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.FORGOT_PASSWORD_APP_MAIL, Constants.FORGOT_PASSWORD_APP_MAIL_SUBJECT, template, Constants.FORGOT_PASSWORD_APP_MAIL_DESC);

        template = Utils.readResourceFile("mailtemplates/" + Constants.FORGOT_PASSWORD_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.FORGOT_PASSWORD_MAIL, Constants.FORGOT_PASSWORD_MAIL_SUBJECT, template, Constants.FORGOT_PASSWORD_MAIL_DESC);

        template = Utils.readResourceFile("mailtemplates/" + Constants.PASSWORD_CHANGED_APP_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.PASSWORD_CHANGED_APP_MAIL, Constants.PASSWORD_CHANGED_APP_MAIL_SUBJECT, template, Constants.PASSWORD_CHANGED_APP_MAIL_DESC);

        template = Utils.readResourceFile("mailtemplates/" + Constants.PASSWORD_CHANGED_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.PASSWORD_CHANGED_MAIL, Constants.PASSWORD_CHANGED_MAIL_SUBJECT, template, Constants.PASSWORD_CHANGED_MAIL_SUBJECT);

        template = Utils.readResourceFile("mailtemplates/" + Constants.SIGNUP_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.SIGNUP_MAIL, Constants.SIGNUP_MAIL_SUBJECT, template, Constants.SIGNUP_MAIL_SUBJECT);

        template = Utils.readResourceFile("mailtemplates/" + Constants.PDF_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.PDF_MAIL, Constants.PDF_MAIL_SUBJECT, template, Constants.PDF_MAIL_DESC);

        template = Utils.readResourceFile("mailtemplates/" + Constants.SHARE_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.SHARE_MAIL, Constants.SHARE_MAIL_SUBJECT, template, Constants.SHARE_MAIL_DESC);

        template = Utils.readResourceFile("mailtemplates/" + Constants.CREATE_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.CREATE_MAIL, Constants.CREATE_MAIL_SUBJECT, template, Constants.CREATE_MAIL_DESC);

        template = Utils.readResourceFile("mailtemplates/" + Constants.GENERAL_MAIL + ".html");
        template = template.replace("{{BLDR-URL}}", companyObject.getFormsadminurl());
        template = template.replace("{{APP-URL}}", companyObject.getFormsappurl());
        template = template.replace("{{UMP-URL}}", umpURL);
        umpRestHelper.createEmailTemplate(companyObject.getCompany(), Constants.GENERAL_MAIL, Constants.GENERAL_MAIL_SUBJECT, template, Constants.GENERAL_MAIL_DESC);
    }

    // Checks for root company initially and if not setup initiates it
    private ModelAndView handleLoggedInSession(SessionObject sessionObject) {
        if (Utils.isUMPAuthenticated(sessionObject)) {
            try {
                // If root company not setup lets do that
                if (!Boolean.valueOf(isRootSetup(sessionObject))) {
                    ModelAndView view = new ModelAndView("discoveryservice");
                    view.addObject("company", sessionObject.getCompany());
                    if (sessionObject.getUmptype().equalsIgnoreCase(("production"))) {
                        view.addObject("formsappurl", "https://" + sessionObject.getCompany().toLowerCase() + "." + Constants.APP_CLOUD_URL);
                        view.addObject("formsadminurl", "https://" + sessionObject.getCompany().toLowerCase() + "." + Constants.ADMIN_CLOUD_URL);
                    } else if (sessionObject.getUmptype().equalsIgnoreCase(("sandbox"))) {
                        view.addObject("formsappurl", "https://" + sessionObject.getCompany().toLowerCase() + ".sbox." + Constants.APP_CLOUD_URL);
                        view.addObject("formsadminurl", "https://" + sessionObject.getCompany().toLowerCase() + ".sbox." + Constants.ADMIN_CLOUD_URL);
                    }
                    else {
                        view.addObject("formsappurl", "");
                        view.addObject("formsadminurl", "");
                    }
                    return view;
                }

                CreateCompanyObject companyObject = redisRepository.getCompany(sessionObject.getUmpUrl() + sessionObject.getCompany());

                if (companyObject == null || companyObject.getStatus() == CreateCompanyObject.CREATE_STATE.NONE) {
                    ModelAndView view = new ModelAndView("newcompany");
                    return view;
                }

                return continueCompanyCreation(companyObject, false);

            } catch (Exception e) {
                e.printStackTrace();
                ModelAndView view = new ModelAndView("umplogin");
                view.addObject("error", "Something went wrong:" + e.getMessage());
                return view;
            }
        }
        return null;
    }
 
    private String runProvisioning(final UmpRestHelper umpHelper, final RegisterDomainObject regInfo)
    {
        String message = null;
        String adminAuthKey = null;
        String apiAuthKey = null;
        
        log.info("Begin: Provisioning of app: " + regInfo.getAppName());
        
        // Run the creation state machine
        while (regInfo.getState() != ProvisionState.DONE && regInfo.getState() != ProvisionState.ERROR) {
            try {
                log.info("Executing state: " + regInfo.getState().name());
                
                switch (regInfo.getState()) {
                    case CREATE_COMPANY:
                        umpHelper.createCompany(regInfo, regInfo.getRandomPassword());
                        log.info("Created company: " + regInfo.getDomain());
                        regInfo.setState(ProvisionState.ASSIGN_APP);
                        break;
                        
                    case ASSIGN_APP:   
                        umpHelper.assignApp(regInfo.getAppName(), regInfo.getDomain() + "\\SA", regInfo.getRandomPassword());
                        log.info("Assigned app: " + regInfo.getAppName());
                        regInfo.setState(ProvisionState.CREATE_ADMIN_USER);                    
                        break;
                        
                    case CREATE_ADMIN_USER:
                        umpHelper.createAdminUser(regInfo);
                        log.info("Created admin user: " + regInfo.getAppName() + "ADMIN");
                        regInfo.setState(ProvisionState.CREATE_API_USER);
                        break;
                        
                    case CREATE_API_USER:
                        umpHelper.createApiUser(regInfo, regInfo.getRootPassword());
                        log.info("Created API user: APIUSER");
                        regInfo.setState(ProvisionState.SAVE_TOKENS);
                        break;
                        
                    case SAVE_TOKENS:
                        //  Get Admin Auth Key
                        Map user = umpHelper.getUser(regInfo.getDomain() + "\\" + regInfo.getAppName() + "_ADMIN", regInfo.getRandomPassword());
                        adminAuthKey = user.get("authKey").toString();  
                        
                        //  Get API Auth Key
                        user = umpHelper.getUser(regInfo.getDomain() + "\\" + regInfo.getAppName() + "_APIUSER", regInfo.getRandomPassword());
                        apiAuthKey = user.get("authKey").toString();
                        
                        log.info("Saved tokens");
                        regInfo.setState(ProvisionState.PROVISION_USER);
                        break;
                        
                    case PROVISION_USER:
                        umpHelper.createFirstUser(regInfo, true);
                        log.info("Created First user with email: " + regInfo.getEmail());                        
                        regInfo.setState(ProvisionState.CREATE_EMAIL_TEMPLATE);
                        break;
                        
                    case CREATE_EMAIL_TEMPLATE:
                        // Do nothing for now
                        log.info("Created Email Templates");
                        regInfo.setState(ProvisionState.PROVISION_APP);
                        break;
    
                    case PROVISION_APP:
                        // Call the PA
                        log.info("Calling PA to finalize provisioning");
                        try {
                            umpHelper.completeProvisioning(regInfo, adminAuthKey, apiAuthKey);
                        } catch (Exception e) { /* Ignore for now */ }
                        
                        regInfo.setState(ProvisionState.DONE);
                        break;
    
                    case DONE:
                    case ERROR:
                        break;
                }
                
                // Save back the state so if we crash we can resume
                domainRepo.updateDomain(regInfo);
            } catch (Exception e) {
                log.error("Error while executing: " + regInfo.getState().name() + ": " + e.getMessage());
                e.printStackTrace();
                
                message = e.getMessage();
                regInfo.setState(ProvisionState.ERROR);
                domainRepo.updateDomain(regInfo);
            }
        }
        
        return message;
    }
    
    private String jsonResult(String message, boolean success)
    {
        JsonObject result = new JsonObject();
        result.addProperty("result", success);
        result.addProperty("message", message);
        
        return new Gson().toJson(result);
    }    
    
    private String generateSecureOTP() {
        SecureRandom secureRandom = new SecureRandom();
        int otp = 100000 + secureRandom.nextInt(900000); // Ensures 6-digit OTP
        return String.valueOf(otp);
    }    
}